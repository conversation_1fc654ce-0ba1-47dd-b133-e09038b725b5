#!/usr/bin/env python3
"""
Database Clearing Script

This script clears all data from the power plant registry database.
Use this for testing purposes or when you need a fresh database state.

Usage:
    python clear_database.py

The script will:
1. Connect to the SQLite database
2. Clear all tables (power_plants_registry, entity_extraction_jobs, plant_extraction_status)
3. Verify that all tables are empty
4. Provide confirmation of successful clearing
"""

import os
import sqlite3
import sys
from pathlib import Path


def get_database_path():
    """Get the path to the database file"""
    # Try different possible locations
    possible_paths = [
        "src/agent/powerplant_registry.db",
        "backend/src/agent/powerplant_registry.db", 
        "agent/powerplant_registry.db",
        "powerplant_registry.db"
    ]
    
    for path in possible_paths:
        if os.path.exists(path):
            return path
    
    # If not found, use the default path
    return "src/agent/powerplant_registry.db"


def clear_database():
    """Clear all data from the database tables"""
    db_path = get_database_path()
    
    print("🗄️ Database Clearing Script")
    print("=" * 50)
    print(f"Database path: {db_path}")
    
    if not os.path.exists(db_path):
        print(f"❌ Database file not found at: {db_path}")
        print("Please make sure you're running this script from the correct directory.")
        return False
    
    try:
        # Connect to the database
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Get all table names
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        
        if not tables:
            print("⚠️ No tables found in database")
            conn.close()
            return True
        
        print(f"📋 Found {len(tables)} tables to clear:")
        
        # Clear each table
        tables_cleared = 0
        for table in tables:
            table_name = table[0]
            
            # Get row count before clearing
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            row_count_before = cursor.fetchone()[0]
            
            if row_count_before > 0:
                print(f"   🧹 Clearing {table_name} ({row_count_before} rows)...")
                cursor.execute(f"DELETE FROM {table_name}")
                tables_cleared += 1
            else:
                print(f"   ✅ {table_name} (already empty)")
        
        # Commit the changes
        conn.commit()
        
        # Verify tables are empty
        print("\n🔍 Verifying tables are cleared:")
        all_empty = True
        for table in tables:
            table_name = table[0]
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            count = cursor.fetchone()[0]
            status = "✅" if count == 0 else "❌"
            print(f"   {status} {table_name}: {count} rows")
            if count > 0:
                all_empty = False
        
        conn.close()
        
        if all_empty:
            print(f"\n✅ Database cleared successfully!")
            print(f"   - {len(tables)} tables processed")
            print(f"   - {tables_cleared} tables had data and were cleared")
            print("   - All tables are now empty")
        else:
            print(f"\n⚠️ Some tables still contain data")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ Error clearing database: {e}")
        return False


def confirm_action():
    """Ask user for confirmation before clearing database"""
    print("⚠️  WARNING: This will permanently delete ALL data from the database!")
    print("   - All plant records will be removed")
    print("   - All organization data will be deleted") 
    print("   - All job tracking information will be lost")
    print()
    
    response = input("Are you sure you want to continue? (type 'yes' to confirm): ").strip().lower()
    
    if response == 'yes':
        return True
    else:
        print("❌ Operation cancelled by user")
        return False


def main():
    """Main function"""
    print("🧹 Power Plant Database Clearing Tool")
    print()
    
    # Ask for confirmation
    if not confirm_action():
        sys.exit(0)
    
    print()
    
    # Clear the database
    success = clear_database()
    
    if success:
        print("\n🎉 Database clearing completed successfully!")
        print("   The database is now ready for fresh data.")
    else:
        print("\n💥 Database clearing failed!")
        print("   Please check the error messages above.")
        sys.exit(1)


if __name__ == "__main__":
    main()
