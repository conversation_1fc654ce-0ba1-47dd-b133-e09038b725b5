"""
Entity-Level Extraction Controller

This module handles the orchestration of multi-plant extraction for entire organizations.
It processes all plants owned by an organization, skipping organization-level extraction
for efficiency and using the existing 3-level extraction pipeline.
"""

import asyncio
import uuid
import time
import json
from datetime import datetime
from typing import List, Dict, Optional, Any
from dataclasses import dataclass

from agent.database_manager import (
    get_database_manager, 
    EntityJobStatus, 
    PlantExtractionStatusEnum,
    ExtractionPhase
)
from agent.utils import get_research_topic
from langchain_core.messages import HumanMessage


@dataclass
class EntityExtractionConfig:
    """Configuration for entity-level extraction"""
    batch_size: int = 3  # Number of plants to process concurrently
    delay_between_batches: int = 60  # Seconds between batches
    max_retries: int = 2  # Max retries per plant
    timeout_per_plant: int = 1800  # 30 minutes timeout per plant


class EntityExtractionController:
    """
    Main controller for entity-level extraction
    
    Orchestrates the processing of all plants in an organization,
    skipping organization-level extraction for efficiency.
    """
    
    def __init__(self, input_plant_name: str, config: EntityExtractionConfig = None):
        """
        Initialize entity extraction controller

        Args:
            input_plant_name: Plant that triggered the entity extraction
            config: Configuration for extraction process
        """
        self.input_plant_name = input_plant_name
        self.config = config or EntityExtractionConfig()
        self.db_manager = get_database_manager()
        self.job_id = None
        self.org_info = None
        
    async def execute_entity_extraction(self) -> Dict[str, Any]:
        """
        Main orchestration method for entity-level extraction
        
        Returns:
            Dictionary with extraction results and statistics
        """
        print(f"🚀 STARTING ENTITY-LEVEL EXTRACTION")
        print(f"   Input plant: {self.input_plant_name}")
        print(f"   Batch size: {self.config.batch_size}")
        
        try:
            # Phase 1: Get organization information
            org_info = await self.get_organization_info()
            if not org_info:
                return {"success": False, "error": "Failed to get organization information"}
            
            # Phase 2: Get all plants for the organization
            plants = await self.get_organization_plants(org_info)
            if not plants:
                return {"success": False, "error": "No plants found for organization"}
            
            # Phase 3: Create entity extraction job
            job_id = await self.create_extraction_job(org_info, plants)
            if not job_id:
                return {"success": False, "error": "Failed to create extraction job"}
            
            # Phase 4: Process plants in batches
            results = await self.process_plants_in_batches(plants, job_id)
            
            # Phase 5: Finalize job and generate report
            final_report = await self.finalize_extraction_job(job_id, results)
            
            return final_report
            
        except Exception as e:
            print(f"❌ Entity extraction failed: {e}")
            if self.job_id:
                # Update job status to failed (run in thread to avoid blocking)
                await asyncio.to_thread(
                    self.db_manager.update_entity_job_status,
                    self.job_id,
                    EntityJobStatus.FAILED,
                    error_log=str(e)
                )
            return {"success": False, "error": str(e)}
    
    async def get_organization_info(self) -> Optional[Dict]:
        """Get organization information - first check database, then run discovery if needed"""
        print(f"🔍 Getting organization info for: {self.input_plant_name}")

        # First, check if plant exists in database
        try:
            plant_info = await asyncio.to_thread(
                self.db_manager.check_plant_exists,
                self.input_plant_name
            )
        except Exception as e:
            print(f"❌ Error checking plant existence: {e}")
            plant_info = None

        if plant_info:
            # Plant found in database - use existing org info
            org_info = {
                "org_name": plant_info["org_name"],
                "org_uid": plant_info["org_uid"],
                "country": plant_info["country"]
            }

            print(f"✅ Organization info retrieved from database:")
            print(f"   Name: {org_info['org_name']}")
            print(f"   UID: {org_info['org_uid']}")
            print(f"   Country: {org_info['country']}")

            self.org_info = org_info
            return org_info

        # Plant not in database - run organization discovery
        print(f"🔍 Plant not in database, running organization discovery...")
        return await self.discover_organization_info()

    async def discover_organization_info(self) -> Optional[Dict]:
        """Run organization discovery using the graph pipeline"""
        print(f"🚀 Starting organization discovery for: {self.input_plant_name}")

        try:
            # Import graph and run organization discovery
            from agent.graph import graph
            from langchain_core.messages import HumanMessage

            # Create state for organization discovery
            discovery_state = {
                "messages": [HumanMessage(content=self.input_plant_name)],
                "session_id": f"entity_discovery_{int(time.time())}",
                "research_topic": self.input_plant_name,
                "current_step": "organization",
                "summaries": [],
                "research_loop": 1,
                "max_research_loops": 3,
                "search_phase": 1
            }

            print(f"🔍 Running organization discovery through graph...")

            # Run the graph to discover organization info
            result = await asyncio.to_thread(graph.invoke, discovery_state)

            # Extract organization data from result
            org_data = result.get("organization_data", {})
            if not org_data:
                print(f"❌ No organization data discovered")
                return None

            # Extract organization info
            country = org_data.get("country_name", "Unknown")

            # Extract organization name from plant name
            org_name = self.input_plant_name
            suffixes_to_remove = [
                " Power Plant", " Power Station", " Plant", " Station",
                " Solar Plant", " Solar Farm", " Solar Park", " Solar",
                " Wind Farm", " Wind Plant", " Thermal Plant"
            ]

            for suffix in suffixes_to_remove:
                if self.input_plant_name.endswith(suffix):
                    org_name = self.input_plant_name[:-len(suffix)].strip()
                    break

            # Generate UIDs
            org_uid = self.db_manager.generate_org_uid(org_name, country)
            plant_uid = self.db_manager.generate_plant_uid(self.input_plant_name, org_uid)

            # Save to database
            plants_data = [{
                "name": self.input_plant_name,
                "status": "operational",
                "country": country
            }]

            success = self.db_manager.save_organization_plants(
                org_name=org_name,
                country=country,
                plants_data=plants_data,
                org_uid=org_uid,
                discovery_session_id=discovery_state["session_id"],
                discovered_from_plant=self.input_plant_name
            )

            if not success:
                print(f"❌ Failed to save organization to database")
                return None

            org_info = {
                "org_name": org_name,
                "org_uid": org_uid,
                "country": country
            }

            print(f"✅ Organization discovered and saved:")
            print(f"   Name: {org_info['org_name']}")
            print(f"   UID: {org_info['org_uid']}")
            print(f"   Country: {org_info['country']}")

            self.org_info = org_info
            return org_info

        except Exception as e:
            print(f"❌ Error in organization discovery: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    async def get_organization_plants(self, org_info: Dict) -> List[Dict]:
        """Get all operational plants for the organization"""
        print(f"📋 Getting all plants for organization: {org_info['org_name']}")

        # Get plants from database (run in thread to avoid blocking)
        plants = await asyncio.to_thread(
            self.db_manager.get_plants_for_entity_extraction,
            org_info["org_uid"]
        )
        
        print(f"✅ Found {len(plants)} operational plants:")
        for i, plant in enumerate(plants, 1):
            print(f"   {i}. {plant['plant_name']} ({plant['country']})")
        
        return plants
    
    async def create_extraction_job(self, org_info: Dict, plants: List[Dict]) -> Optional[str]:
        """Create entity extraction job in database"""
        print(f"📝 Creating entity extraction job...")

        try:
            # Create job in database (run in thread to avoid blocking)
            job_id = await asyncio.to_thread(
                self.db_manager.create_entity_extraction_job,
                org_name=org_info["org_name"],
                org_uid=org_info["org_uid"],
                input_plant_name=self.input_plant_name,
                total_plants=len(plants)
            )
            
            self.job_id = job_id
            print(f"✅ Entity extraction job created: {job_id}")
            return job_id
            
        except Exception as e:
            print(f"❌ Failed to create extraction job: {e}")
            return None
    
    async def process_plants_in_batches(self, plants: List[Dict], job_id: str) -> Dict:
        """
        Process plants in intelligent batches
        
        Args:
            plants: List of plant dictionaries
            job_id: Entity extraction job ID
            
        Returns:
            Dictionary with processing results
        """
        print(f"🔄 Processing {len(plants)} plants in batches of {self.config.batch_size}")
        
        results = {
            "completed": [],
            "failed": [],
            "skipped": [],
            "total_processed": 0
        }
        
        # Update job status to running (run in thread to avoid blocking)
        await asyncio.to_thread(
            self.db_manager.update_entity_job_status,
            job_id,
            EntityJobStatus.RUNNING
        )
        
        # Process plants in batches
        for i in range(0, len(plants), self.config.batch_size):
            batch = plants[i:i + self.config.batch_size]
            batch_num = (i // self.config.batch_size) + 1
            total_batches = (len(plants) + self.config.batch_size - 1) // self.config.batch_size
            
            print(f"📦 Processing batch {batch_num}/{total_batches} ({len(batch)} plants)")
            
            # Process batch (sequential for now, can be made concurrent later)
            batch_results = await self.process_plant_batch(batch, job_id)
            
            # Aggregate results
            results["completed"].extend(batch_results["completed"])
            results["failed"].extend(batch_results["failed"])
            results["skipped"].extend(batch_results["skipped"])
            results["total_processed"] += len(batch)
            
            # Update job progress (run in thread to avoid blocking)
            await asyncio.to_thread(
                self.db_manager.update_entity_job_status,
                job_id,
                EntityJobStatus.RUNNING,
                completed_plants=len(results["completed"]),
                failed_plants=len(results["failed"])
            )
            
            # Progress update
            print(f"📊 Batch {batch_num} complete. Progress: {results['total_processed']}/{len(plants)}")
            
            # Delay between batches (except for last batch)
            if i + self.config.batch_size < len(plants):
                print(f"⏳ Waiting {self.config.delay_between_batches}s before next batch...")
                await asyncio.sleep(self.config.delay_between_batches)
        
        return results
    
    async def process_plant_batch(self, batch: List[Dict], job_id: str) -> Dict:
        """
        Process a single batch of plants
        
        Args:
            batch: List of plants in this batch
            job_id: Entity extraction job ID
            
        Returns:
            Dictionary with batch results
        """
        batch_results = {"completed": [], "failed": [], "skipped": []}
        
        for plant in batch:
            plant_name = plant["plant_name"]
            plant_uid = plant["plant_uid"]
            
            print(f"🏭 Processing plant: {plant_name}")
            
            try:
                # Skip organization level, process only plant/unit/transition levels
                result = await self.process_single_plant(plant, job_id)
                
                if result["success"]:
                    batch_results["completed"].append({
                        "plant_name": plant_name,
                        "plant_uid": plant_uid,
                        "s3_urls": result.get("s3_urls", [])
                    })
                    print(f"✅ {plant_name} completed successfully")
                else:
                    batch_results["failed"].append({
                        "plant_name": plant_name,
                        "plant_uid": plant_uid,
                        "error": result.get("error", "Unknown error")
                    })
                    print(f"❌ {plant_name} failed: {result.get('error', 'Unknown error')}")
                    
            except Exception as e:
                batch_results["failed"].append({
                    "plant_name": plant_name,
                    "plant_uid": plant_uid,
                    "error": str(e)
                })
                print(f"❌ {plant_name} failed with exception: {e}")
        
        return batch_results

    async def process_single_plant(self, plant: Dict, job_id: str) -> Dict:
        """
        Process a single plant using existing 3-level pipeline (skip org level)

        Args:
            plant: Plant dictionary
            job_id: Entity extraction job ID

        Returns:
            Dictionary with processing result
        """
        plant_name = plant["plant_name"]
        plant_uid = plant["plant_uid"]

        print(f"🔧 Processing {plant_name} - FULL Organization/Plant/Unit/Transition levels")

        try:
            # Import the existing graph for 3-level extraction
            from agent.graph import graph
            from agent.state import OverallState

            # Create a new session for this plant
            session_id = f"entity-{job_id}-{plant_uid[:8]}"

            # Create state for FULL 3-level extraction (preserve AGI context)
            state = {
                "messages": [HumanMessage(content=plant_name)],
                "session_id": session_id,
                "search_phase": 1,  # START FROM ORGANIZATION LEVEL (full extraction)
                "research_loop_count": 0,
                "web_research_result": [],
                "search_query": [],
                "sources_gathered": [],
                "org_level_complete": False,  # DO FULL EXTRACTION
                "continue_research": False,
                "phase_complete": False,
                "initial_search_query_count": 5,
                "max_research_loops": 3,
                "reasoning_model": "",
                # CRITICAL: Include entity_id so AGI context is preserved
                "entity_id": self.org_info["org_uid"],  # Pass AGI UID as entity_id
                # S3 JSON Storage initialization
                "plant_name_for_s3": plant_name,
                "s3_json_urls": {},
                "json_storage_complete": {"organization": False, "plant": False},  # ORG + PLANT EXTRACTION
                "json_storage_errors": [],
            }

            # SIMPLE: org_uid is already correct in database (AGI UID)
            print(f"ℹ️ Entity extraction using org_uid from database: {self.org_info['org_uid']}")

            print(f"🚀 Starting FULL 3-level extraction for {plant_name} (session: {session_id})")
            print(f"   Starting from organization level (phase 1)")
            print(f"   Using AGI UID: {self.org_info['org_uid']}")
            print(f"   AGI context preserved via entity_id field")

            # Execute the graph starting from plant level
            result = await asyncio.to_thread(graph.invoke, state)

            # Extract S3 URLs from result
            s3_urls = result.get("s3_json_urls", {})
            s3_url_list = []
            if isinstance(s3_urls, dict):
                for key, url in s3_urls.items():
                    if url:
                        s3_url_list.append(url)

            print(f"✅ {plant_name} extraction completed")
            print(f"   S3 URLs generated: {len(s3_url_list)}")

            return {
                "success": True,
                "session_id": session_id,
                "s3_urls": s3_url_list,
                "extraction_phases": ["organization", "plant"],  # Only org and plant levels
                "result": result
            }

        except Exception as e:
            print(f"❌ {plant_name} extraction failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "session_id": session_id if 'session_id' in locals() else None
            }

    async def finalize_extraction_job(self, job_id: str, results: Dict) -> Dict:
        """
        Finalize the extraction job and generate final report

        Args:
            job_id: Entity extraction job ID
            results: Processing results

        Returns:
            Final extraction report
        """
        print(f"📋 Finalizing entity extraction job: {job_id}")

        # Update job status to completed (run in thread to avoid blocking)
        await asyncio.to_thread(
            self.db_manager.update_entity_job_status,
            job_id,
            EntityJobStatus.COMPLETED,
            completed_plants=len(results["completed"]),
            failed_plants=len(results["failed"])
        )

        # Generate final report
        report = {
            "success": True,
            "job_id": job_id,
            "organization": self.org_info["org_name"],
            "input_plant": self.input_plant_name,
            "total_plants": results["total_processed"],
            "completed_plants": len(results["completed"]),
            "failed_plants": len(results["failed"]),
            "skipped_plants": len(results["skipped"]),
            "success_rate": len(results["completed"]) / results["total_processed"] * 100 if results["total_processed"] > 0 else 0,
            "completed_at": datetime.now().isoformat(),
            "results": results
        }

        print(f"✅ ENTITY EXTRACTION COMPLETED")
        print(f"   Organization: {report['organization']}")
        print(f"   Total plants: {report['total_plants']}")
        print(f"   Completed: {report['completed_plants']}")
        print(f"   Failed: {report['failed_plants']}")
        print(f"   Success rate: {report['success_rate']:.1f}%")

        return report
