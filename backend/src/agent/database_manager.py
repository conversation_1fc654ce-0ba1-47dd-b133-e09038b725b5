"""
Power Plant Registry Database Manager

This module handles the database operations for the power plant registry system.
Supports both SQLite (development) and PostgreSQL (production) through SQLAlchemy.
"""

import os
import hashlib
import time
import uuid
from datetime import datetime
from typing import List, Dict, Optional, Tuple
from sqlalchemy import create_engine, Column, Integer, String, DateTime, Enum, Index, text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.exc import IntegrityError, SQLAlchemyError
import enum

# Database configuration
Base = declarative_base()

class PlantStatus(enum.Enum):
    OPERATIONAL = "operational"
    UNDER_CONSTRUCTION = "under_construction"
    DECOMMISSIONED = "decommissioned"
    RETIRED = "retired"
    UNKNOWN = "unknown"

class DiscoveryStatus(enum.Enum):
    PARTIAL = "partial"  # Only basic org info discovered
    COMPLETE = "complete"  # Full plant list discovered
    FAILED = "failed"  # Discovery failed

class EntityJobStatus(enum.Enum):
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    PAUSED = "paused"

class PlantExtractionStatusEnum(enum.Enum):
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    SKIPPED = "skipped"

class ExtractionPhase(enum.Enum):
    NOT_STARTED = "not_started"
    ORGANIZATION = "organization"
    PLANT = "plant"
    UNIT_EXTRACTION = "unit_extraction"
    TRANSITION_PLAN = "transition_plan"
    COMPLETED = "completed"

class PowerPlantRegistry(Base):
    """
    Database model for power plant registry
    
    This table stores information about power plants and their parent organizations.
    Each organization gets a unique UID, and all plants under that organization
    share the same org_uid.
    """
    __tablename__ = 'power_plants_registry'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    org_name = Column(String(255), nullable=False, index=True)
    plant_name = Column(String(255), nullable=False)
    country = Column(String(100), nullable=False)
    org_uid = Column(String(50), nullable=False, index=True)
    plant_uid = Column(String(50), nullable=True, index=True)  # NEW: Plant-specific UID
    plant_status = Column(Enum(PlantStatus), default=PlantStatus.OPERATIONAL)
    discovery_status = Column(Enum(DiscoveryStatus), default=DiscoveryStatus.PARTIAL)
    discovery_session_id = Column(String(50), nullable=True)
    discovered_from_plant = Column(String(255), nullable=True)  # Which plant triggered the discovery
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Indexes for performance
    __table_args__ = (
        Index('idx_plant_country', 'plant_name', 'country'),
        Index('idx_org_uid', 'org_uid'),
        Index('idx_org_name', 'org_name'),
    )

class EntityExtractionJob(Base):
    """
    Database model for entity-level extraction job tracking

    This table tracks multi-plant extraction jobs for entire organizations.
    Each job represents processing all plants for a single organization.
    """
    __tablename__ = 'entity_extraction_jobs'

    job_id = Column(String(50), primary_key=True)
    org_name = Column(String(255), nullable=False, index=True)
    org_uid = Column(String(50), nullable=False, index=True)
    input_plant_name = Column(String(255), nullable=False)  # Plant that triggered the job
    total_plants = Column(Integer, default=0)
    completed_plants = Column(Integer, default=0)
    failed_plants = Column(Integer, default=0)
    status = Column(Enum(EntityJobStatus), default=EntityJobStatus.PENDING)
    error_log = Column(String(1000), nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    completed_at = Column(DateTime, nullable=True)

    # Indexes for performance
    __table_args__ = (
        Index('idx_entity_job_org', 'org_uid'),
        Index('idx_entity_job_status', 'status'),
    )

class PlantExtractionStatus(Base):
    """
    Database model for tracking individual plant extraction status within entity jobs
    """
    __tablename__ = 'plant_extraction_status'

    id = Column(Integer, primary_key=True, autoincrement=True)
    entity_job_id = Column(String(50), nullable=False, index=True)
    plant_uid = Column(String(50), nullable=False, index=True)
    plant_name = Column(String(255), nullable=False)
    status = Column(Enum(PlantExtractionStatusEnum), default=PlantExtractionStatusEnum.PENDING)
    extraction_phase = Column(Enum(ExtractionPhase), default=ExtractionPhase.NOT_STARTED)
    session_id = Column(String(50), nullable=True)
    s3_urls = Column(String(2000), nullable=True)  # JSON string of S3 URLs
    error_message = Column(String(1000), nullable=True)
    started_at = Column(DateTime, nullable=True)
    completed_at = Column(DateTime, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Indexes for performance
    __table_args__ = (
        Index('idx_plant_extraction_job', 'entity_job_id'),
        Index('idx_plant_extraction_status', 'status'),
    )

class DatabaseManager:
    """
    Database manager for power plant registry operations
    
    Handles all database operations including plant registry, UID generation,
    and entity extraction job tracking.
    """
    
    def __init__(self, database_url: str = None):
        """
        Initialize database manager
        
        Args:
            database_url: Database connection URL. If None, uses SQLite default.
        """
        if database_url is None:
            # Default to SQLite for development
            db_path = os.path.join(os.path.dirname(__file__), "powerplant_registry.db")
            database_url = f"sqlite:///{db_path}"
        
        self.database_url = database_url
        self.engine = create_engine(database_url, echo=False)
        self.SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=self.engine)
        
        # Create tables
        Base.metadata.create_all(bind=self.engine)
        print(f"🗄️ Database initialized: {database_url}")
    
    def get_session(self) -> Session:
        """Get database session"""
        return self.SessionLocal()
    
    def get_org_uid_from_database(self, org_name: str, country: str = None) -> Optional[str]:
        """
        Get organization UID from database

        Args:
            org_name: Organization name to search for
            country: Country name (optional, for better matching)

        Returns:
            Organization UID from database, or None if not found
        """
        session = self.get_session()
        try:
            # Search for existing organization by name
            query = session.query(PowerPlantRegistry).filter_by(org_name=org_name)

            # Add country filter if provided
            if country:
                query = query.filter_by(country=country)

            existing_org = query.first()

            if existing_org and existing_org.org_uid:
                print(f"✅ Found existing org UID in database: {existing_org.org_uid} for organization: {org_name}")
                return existing_org.org_uid
            else:
                print(f"❌ No org UID found in database for organization: {org_name}")
                return None

        except Exception as e:
            print(f"❌ Error searching for org UID in database: {e}")
            return None
        finally:
            session.close()

    def store_org_uid_in_database(self, org_name: str, org_uid: str, country: str = "Unknown") -> bool:
        """
        Store organization UID (entity_id) in database

        Args:
            org_name: Organization name
            org_uid: Organization UID (AGI entity_id)
            country: Country name

        Returns:
            True if successful, False otherwise
        """
        session = self.get_session()
        try:
            # Check if organization already exists
            existing_org = session.query(PowerPlantRegistry).filter_by(
                org_name=org_name,
                org_uid=org_uid
            ).first()

            if existing_org:
                print(f"✅ Organization already exists in database: {org_name} (UID: {org_uid})")
                return True

            # Create new organization entry
            new_org_entry = PowerPlantRegistry(
                org_name=org_name,
                plant_name="ORGANIZATION_PLACEHOLDER",  # Placeholder for org-only entry
                country=country,
                org_uid=org_uid,
                plant_uid=None,  # No plant UID for org-only entry
                plant_status=PlantStatus.OPERATIONAL,
                discovery_status=DiscoveryStatus.PARTIAL
            )

            session.add(new_org_entry)
            session.commit()

            print(f"✅ Stored organization UID in database: {org_name} (UID: {org_uid})")
            return True

        except Exception as e:
            print(f"❌ Error storing org UID in database: {e}")
            session.rollback()
            return False
        finally:
            session.close()

    def get_plant_uid_from_database(self, plant_name: str, org_uid: str) -> Optional[str]:
        """
        Get plant UID from database

        Args:
            plant_name: Plant name to search for
            org_uid: Organization UID

        Returns:
            Plant UID from database, or None if not found
        """
        session = self.get_session()
        try:
            existing_plant = session.query(PowerPlantRegistry).filter_by(
                plant_name=plant_name,
                org_uid=org_uid
            ).first()

            if existing_plant and existing_plant.plant_uid:
                print(f"✅ Found existing plant UID in database: {existing_plant.plant_uid} for plant: {plant_name}")
                return existing_plant.plant_uid
            else:
                print(f"❌ No plant UID found in database for plant: {plant_name}")
                return None

        except Exception as e:
            print(f"❌ Error searching for plant UID in database: {e}")
            return None
        finally:
            session.close()

    def generate_and_store_plant_uid(self, plant_name: str, org_uid: str, org_name: str, country: str = "Unknown") -> str:
        """
        Generate new plant UID and store in database

        Args:
            plant_name: Plant name
            org_uid: Organization UID (can be None for temporary processing)
            org_name: Organization name
            country: Country name

        Returns:
            Generated plant UID
        """
        # Generate new plant UID
        plant_uid = str(uuid.uuid4())

        # If org_uid is None or temporary, don't store in database
        if not org_uid or str(org_uid).startswith("temp_org_"):
            print(f"⚠️ Temporary org_uid detected, not storing in database: {plant_name} (UID: {plant_uid})")
            return plant_uid

        session = self.get_session()
        try:
            # Check if plant already exists
            existing_plant = session.query(PowerPlantRegistry).filter_by(
                plant_name=plant_name,
                org_uid=org_uid
            ).first()

            if existing_plant:
                # Update existing plant with new UID
                existing_plant.plant_uid = plant_uid
                existing_plant.updated_at = datetime.utcnow()
                print(f"✅ Updated existing plant with new UID: {plant_name} (UID: {plant_uid})")
            else:
                # Create new plant entry
                new_plant = PowerPlantRegistry(
                    org_name=org_name,
                    plant_name=plant_name,
                    country=country,
                    org_uid=org_uid,
                    plant_uid=plant_uid,
                    plant_status=PlantStatus.OPERATIONAL,
                    discovery_status=DiscoveryStatus.PARTIAL
                )
                session.add(new_plant)
                print(f"✅ Created new plant entry: {plant_name} (UID: {plant_uid})")

            session.commit()
            return plant_uid

        except Exception as e:
            print(f"❌ Error generating/storing plant UID: {e}")
            session.rollback()
            # Return the generated UUID even if storage failed
            return plant_uid
        finally:
            session.close()
    
    def _normalize_plant_status(self, raw_status: str) -> PlantStatus:
        """
        Normalize plant status string to PlantStatus enum
        
        Args:
            raw_status: Raw status string from data source
            
        Returns:
            Normalized PlantStatus enum value
        """
        if not raw_status:
            return PlantStatus.OPERATIONAL
        
        # Normalize to lowercase for comparison
        normalized = raw_status.lower().strip()
        
        # Status mapping
        status_mapping = {
            "operational": PlantStatus.OPERATIONAL,
            "operating": PlantStatus.OPERATIONAL,
            "active": PlantStatus.OPERATIONAL,
            "running": PlantStatus.OPERATIONAL,
            "under construction": PlantStatus.UNDER_CONSTRUCTION,
            "construction": PlantStatus.UNDER_CONSTRUCTION,
            "building": PlantStatus.UNDER_CONSTRUCTION,
            "decommissioned": PlantStatus.DECOMMISSIONED,
            "decommissioning": PlantStatus.DECOMMISSIONED,
            "retired": PlantStatus.RETIRED,
            "shutdown": PlantStatus.RETIRED,
            "closed": PlantStatus.RETIRED,
            "unknown": PlantStatus.UNKNOWN,
            "": PlantStatus.OPERATIONAL  # Default for empty string
        }

        result = status_mapping.get(normalized, PlantStatus.OPERATIONAL)
        print(f"   🔧 Status normalization: '{raw_status}' → '{result.value}'")
        return result

    def generate_plant_uid(self, plant_name: str, org_uid: str) -> str:
        """
        Generate unique plant UID in UUID format

        Format: UUID v4 (e.g., aa4b6731-3969-4ac5-a219-fd1266e15ef6)

        Args:
            plant_name: Plant name (used for logging/tracking only)
            org_uid: Organization UID (used for logging/tracking only)

        Returns:
            Unique plant UID in UUID format
        """
        # Generate UUID v4 for plant
        plant_uid = str(uuid.uuid4())

        print(f"🔧 Generated plant UID: {plant_uid} for plant: {plant_name} (org: {org_uid})")

        # Ensure uniqueness (very rare collision case for UUIDs)
        session = self.get_session()
        try:
            existing = session.query(PowerPlantRegistry).filter_by(plant_uid=plant_uid).first()
            if existing:
                # Generate new UUID if collision occurs (extremely rare)
                plant_uid = str(uuid.uuid4())
                print(f"🔧 UUID collision detected, generated new plant UID: {plant_uid}")

            return plant_uid
        finally:
            session.close()


    def save_organization_plants(
        self,
        org_name: str,
        country: str,
        plants_data: List[Dict],
        org_uid: str = None,
        discovery_session_id: str = None,
        discovered_from_plant: str = None
    ) -> bool:
        """
        Save organization and its plants to database

        Args:
            org_name: Organization name
            country: Country name
            plants_data: List of plant dictionaries
            org_uid: Optional organization UID (if not provided, will generate)
            discovery_session_id: Session ID that discovered these plants
            discovered_from_plant: Plant name that triggered the discovery

        Returns:
            True if successful, False otherwise
        """
        session = self.get_session()
        try:
            # Handle org_uid: use provided, get from database, or store new one
            if not org_uid:
                org_uid = self.get_org_uid_from_database(org_name, country)
                if not org_uid:
                    print(f"❌ Cannot save organization: No org_uid provided and none found in database")
                    print(f"🚨 CRITICAL: org_uid must be provided (AGI entity_id)")
                    return False
            else:
                # Store the provided org_uid in database for future lookups
                self.store_org_uid_in_database(org_name, org_uid, country)

            print(f"💾 Saving organization: {org_name} (UID: {org_uid})")
            print(f"   Country: {country}")
            print(f"   Plants to save: {len(plants_data)}")

            plants_saved = 0
            for plant_info in plants_data:
                plant_name = plant_info.get("name", "Unknown Plant")

                # Use country from plant data if available, otherwise use org country
                plant_country = plant_info.get("country", country) or country
                print(f"   🔧 Processing plant: {plant_name} in country: {plant_country}")

                # Check if plant already exists
                existing = session.query(PowerPlantRegistry).filter_by(
                    plant_name=plant_name
                ).first()  # Remove country filter to avoid duplicates with different countries

                if existing:
                    # Update existing record
                    existing.org_name = org_name  # Update org name
                    existing.country = plant_country  # Update country
                    existing.org_uid = org_uid
                    existing.discovery_status = DiscoveryStatus.COMPLETE
                    existing.updated_at = datetime.utcnow()

                    # Generate plant UID if missing
                    if not existing.plant_uid:
                        existing.plant_uid = self.generate_and_store_plant_uid(plant_name, org_uid, org_name, plant_country)
                        print(f"   🔧 Generated plant UID for existing plant: {existing.plant_uid}")

                    print(f"   ✅ Updated existing plant: {plant_name} in {plant_country}")
                else:
                    # Generate plant UID for new plant
                    plant_uid = self.generate_and_store_plant_uid(plant_name, org_uid, org_name, plant_country)

                    # CRITICAL FIX: Normalize plant status to handle case variations
                    raw_status = plant_info.get("status", "operational")
                    normalized_status = self._normalize_plant_status(raw_status)

                    # Create new plant record
                    plant_record = PowerPlantRegistry(
                        org_name=org_name,
                        plant_name=plant_name,
                        country=plant_country,  # Use plant-specific country
                        org_uid=org_uid,
                        plant_uid=plant_uid,
                        plant_status=normalized_status,
                        discovery_status=DiscoveryStatus.COMPLETE,
                        discovery_session_id=discovery_session_id,
                        discovered_from_plant=discovered_from_plant
                    )
                    session.add(plant_record)
                    print(f"   🔧 Generated plant UID for new plant: {plant_uid}")
                    print(f"   ✅ Created new plant: {plant_name} in {plant_country}")

                plants_saved += 1

            session.commit()
            print(f"✅ Saved {plants_saved} plants for organization: {org_name}")
            return True

        except Exception as e:
            session.rollback()
            print(f"❌ Error saving organization plants: {e}")
            return False
        finally:
            session.close()

    def check_plant_exists(self, plant_name: str) -> Optional[Dict]:
        """
        Check if a plant exists in the database

        Args:
            plant_name: Plant name to check

        Returns:
            Plant information dictionary if found, None otherwise
        """
        session = self.get_session()
        try:
            plant = session.query(PowerPlantRegistry).filter_by(plant_name=plant_name).first()

            if plant:
                return {
                    "id": plant.id,
                    "org_name": plant.org_name,
                    "plant_name": plant.plant_name,
                    "country": plant.country,
                    "org_uid": plant.org_uid,
                    "plant_uid": plant.plant_uid,
                    "plant_status": plant.plant_status.value,
                    "discovery_status": plant.discovery_status.value,
                    "created_at": plant.created_at.isoformat() if plant.created_at else None,
                    "updated_at": plant.updated_at.isoformat() if plant.updated_at else None
                }
            return None

        except Exception as e:
            print(f"❌ Error checking plant existence: {e}")
            return None
        finally:
            session.close()

    def get_plants_by_org_uid(self, org_uid: str) -> List[Dict]:
        """
        Get all plants for an organization by org_uid

        Args:
            org_uid: Organization UID

        Returns:
            List of plant dictionaries
        """
        session = self.get_session()
        try:
            plants = session.query(PowerPlantRegistry).filter_by(org_uid=org_uid).all()

            result = []
            for plant in plants:
                result.append({
                    "id": plant.id,
                    "org_name": plant.org_name,
                    "plant_name": plant.plant_name,
                    "country": plant.country,
                    "org_uid": plant.org_uid,
                    "plant_uid": plant.plant_uid,
                    "plant_status": plant.plant_status.value,
                    "discovery_status": plant.discovery_status.value,
                    "created_at": plant.created_at.isoformat() if plant.created_at else None,
                    "updated_at": plant.updated_at.isoformat() if plant.updated_at else None
                })

            return result

        except Exception as e:
            print(f"❌ Error getting plants by org_uid: {e}")
            return []
        finally:
            session.close()

    def generate_plant_uids_for_existing_plants(self) -> bool:
        """
        Generate plant UIDs for existing plants that don't have them

        Returns:
            True if successful, False otherwise
        """
        session = self.get_session()
        try:
            # Find plants without plant_uid
            plants_without_uid = session.query(PowerPlantRegistry).filter(
                PowerPlantRegistry.plant_uid.is_(None)
            ).all()

            if not plants_without_uid:
                print("✅ All plants already have plant UIDs")
                return True

            print(f"🔧 Generating plant UIDs for {len(plants_without_uid)} plants...")

            updated_count = 0
            for plant in plants_without_uid:
                try:
                    # Generate plant UID
                    plant_uid = self.generate_plant_uid(plant.plant_name, plant.org_uid)

                    # Update the plant record
                    plant.plant_uid = plant_uid
                    plant.updated_at = datetime.utcnow()

                    updated_count += 1
                    print(f"   ✅ {plant.plant_name} → {plant_uid}")

                except Exception as e:
                    print(f"   ❌ Failed to generate UID for {plant.plant_name}: {e}")
                    continue

            session.commit()
            print(f"✅ Generated plant UIDs for {updated_count} plants")
            return True

        except Exception as e:
            session.rollback()
            print(f"❌ Error generating plant UIDs: {e}")
            return False
        finally:
            session.close()

    def save_agi_organization_data(
        self,
        plant_name: str,
        org_uid: str,
        org_name: str = None,
        country: str = None,
        session_id: str = None
    ) -> bool:
        """
        Save AGI organization data to database

        This method handles AGI integration where the org_uid is provided
        by the AGI Layer and needs to be saved to the database.

        Args:
            plant_name: Plant name that triggered the save
            org_uid: AGI-provided organization UID
            org_name: Organization name (optional, will be extracted if not provided)
            country: Country name (optional, will be extracted if not provided)
            session_id: Session ID for tracking

        Returns:
            True if successful, False otherwise
        """
        session = self.get_session()
        try:
            print(f"💾 Saving AGI organization data")
            print(f"   Plant: {plant_name}")
            print(f"   AGI Org UID: {org_uid}")
            print(f"   Org Name: {org_name}")
            print(f"   Country: {country}")

            # Check if plant already exists
            existing = session.query(PowerPlantRegistry).filter_by(
                plant_name=plant_name
            ).first()

            if existing:
                # Update existing record with AGI org_uid
                existing.org_uid = org_uid
                if org_name:
                    existing.org_name = org_name
                if country:
                    existing.country = country
                existing.discovery_status = DiscoveryStatus.COMPLETE
                existing.updated_at = datetime.utcnow()

                # Generate plant UID if missing
                if not existing.plant_uid:
                    existing.plant_uid = self.generate_plant_uid(plant_name, org_uid)
                    print(f"   🔧 Generated plant UID for existing plant: {existing.plant_uid}")

                print(f"✅ Updated existing plant with AGI org_uid: {org_uid}")
            else:
                # Create new plant record with AGI org_uid
                plant_uid = self.generate_plant_uid(plant_name, org_uid)

                plant_record = PowerPlantRegistry(
                    org_name=org_name or "Unknown Organization",
                    plant_name=plant_name,
                    country=country or "Unknown",
                    org_uid=org_uid,
                    plant_uid=plant_uid,
                    plant_status=PlantStatus.OPERATIONAL,
                    discovery_status=DiscoveryStatus.COMPLETE,
                    discovery_session_id=session_id,
                    discovered_from_plant=plant_name
                )
                session.add(plant_record)
                print(f"   🔧 Generated plant UID for new plant: {plant_uid}")
                print(f"✅ Created new plant record with AGI org_uid: {org_uid}")

            session.commit()
            return True

        except Exception as e:
            session.rollback()
            print(f"❌ Error saving AGI organization data: {e}")
            return False
        finally:
            session.close()

    def get_plant_uid_by_name(self, plant_name: str) -> Optional[str]:
        """
        Get plant UID by plant name

        Args:
            plant_name: Plant name to look up

        Returns:
            Plant UID if found, None otherwise
        """
        session = self.get_session()
        try:
            plant = session.query(PowerPlantRegistry).filter_by(plant_name=plant_name).first()
            return plant.plant_uid if plant else None
        except Exception as e:
            print(f"❌ Error getting plant UID: {e}")
            return None
        finally:
            session.close()

    # ===== ENTITY-LEVEL EXTRACTION METHODS =====

    def create_entity_extraction_job(self, org_name: str, org_uid: str, input_plant_name: str,
                                   total_plants: int) -> str:
        """
        Create a new entity-level extraction job

        Args:
            org_name: Organization name
            org_uid: Organization UID
            input_plant_name: Plant that triggered the extraction
            total_plants: Total number of plants to process

        Returns:
            Job ID string
        """
        import uuid
        job_id = str(uuid.uuid4())

        session = self.get_session()
        try:
            job = EntityExtractionJob(
                job_id=job_id,
                org_name=org_name,
                org_uid=org_uid,
                input_plant_name=input_plant_name,
                total_plants=total_plants,
                status=EntityJobStatus.PENDING
            )
            session.add(job)
            session.commit()

            print(f"📝 Created entity extraction job: {job_id}")
            print(f"   Organization: {org_name}")
            print(f"   Total plants: {total_plants}")

            return job_id

        except Exception as e:
            session.rollback()
            print(f"❌ Error creating entity extraction job: {e}")
            raise
        finally:
            session.close()

    def update_entity_job_status(self, job_id: str, status: EntityJobStatus,
                               completed_plants: int = None, failed_plants: int = None,
                               error_log: str = None):
        """
        Update entity extraction job status

        Args:
            job_id: Job ID
            status: New status
            completed_plants: Number of completed plants
            failed_plants: Number of failed plants
            error_log: Error message if failed
        """
        session = self.get_session()
        try:
            job = session.query(EntityExtractionJob).filter_by(job_id=job_id).first()
            if not job:
                print(f"❌ Entity job not found: {job_id}")
                return

            job.status = status
            job.updated_at = datetime.utcnow()

            if completed_plants is not None:
                job.completed_plants = completed_plants

            if failed_plants is not None:
                job.failed_plants = failed_plants

            if error_log:
                job.error_log = error_log

            if status == EntityJobStatus.COMPLETED:
                job.completed_at = datetime.utcnow()

            session.commit()
            print(f"📊 Updated entity job {job_id}: {status.value}")

        except Exception as e:
            session.rollback()
            print(f"❌ Error updating entity job status: {e}")
        finally:
            session.close()

    def get_plants_for_entity_extraction(self, org_uid: str) -> List[Dict]:
        """
        Get all operational plants for entity extraction

        Args:
            org_uid: Organization UID

        Returns:
            List of plant dictionaries suitable for entity extraction
        """
        session = self.get_session()
        try:
            plants = session.query(PowerPlantRegistry).filter(
                PowerPlantRegistry.org_uid == org_uid,
                PowerPlantRegistry.plant_status == PlantStatus.OPERATIONAL
            ).all()

            result = []
            for plant in plants:
                result.append({
                    "plant_name": plant.plant_name,
                    "plant_uid": plant.plant_uid,
                    "country": plant.country,
                    "org_name": plant.org_name,
                    "org_uid": plant.org_uid,
                    "plant_status": plant.plant_status.value
                })

            return result

        except Exception as e:
            print(f"❌ Error getting plants for entity extraction: {e}")
            return []
        finally:
            session.close()

    def test_connection(self) -> bool:
        """
        Test database connection

        Returns:
            True if connection successful, False otherwise
        """
        try:
            session = self.get_session()
            session.execute(text("SELECT 1"))
            session.close()
            print("✅ Database connection successful")
            return True
        except Exception as e:
            print(f"❌ Database connection failed: {e}")
            return False


# Global database manager instance
_db_manager = None

def get_database_manager() -> DatabaseManager:
    """Get global database manager instance"""
    global _db_manager
    if _db_manager is None:
        _db_manager = DatabaseManager()
    return _db_manager
