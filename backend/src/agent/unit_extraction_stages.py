"""
Multi-stage unit data extraction for better API call efficiency and data quality
"""

import os
import json
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_core.messages import HumanMessage

def parse_json_response(content: str, stage_name: str) -> dict:
    """Enhanced JSON parsing with better error handling and logging"""
    print(f"🔍 {stage_name} raw response length: {len(content)} chars")
    print(f"🔍 Response preview: {content[:200]}...")
    
    # Method 1: Try to extract JSON from the response using brackets
    start_idx = content.find('{')
    end_idx = content.rfind('}') + 1
    
    if start_idx != -1 and end_idx != -1:
        try:
            json_str = content[start_idx:end_idx]
            parsed_data = json.loads(json_str)
            print(f"✅ {stage_name} JSON parsed successfully with {len(parsed_data)} fields")
            return parsed_data
        except json.JSONDecodeError as e:
            print(f"⚠️ Failed to parse extracted JSON for {stage_name}: {e}")
    
    # Method 2: Try parsing the whole response as JSON
    try:
        parsed_data = json.loads(content)
        print(f"✅ Full {stage_name} response parsed as JSON with {len(parsed_data)} fields")  
        return parsed_data
    except json.JSONDecodeError as e:
        print(f"❌ Failed to parse full response as JSON for {stage_name}: {e}")
        
    # Method 3: Extract JSON using regex as fallback
    import re
    json_pattern = r'\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}'
    matches = re.findall(json_pattern, content, re.DOTALL)
    
    if matches:
        for match in matches:
            try:
                parsed_data = json.loads(match)
                print(f"✅ {stage_name} JSON extracted via regex with {len(parsed_data)} fields")
                return parsed_data
            except json.JSONDecodeError:
                continue
    
    print(f"❌ All JSON parsing methods failed for {stage_name}")
    return {}


def extract_basic_unit_info(summaries: str, unit_number: str, plant_context: dict, reasoning_model: str) -> dict:
    """Stage 1: Extract basic unit identification and capacity information"""
    
    prompt = f"""Extract basic information for Unit {unit_number} from the research data below.

EXTRACTION STRATEGY (use in this order):
1. PRIORITY 1: Look for Unit {unit_number} specific information first
2. PRIORITY 2: If unit-specific data unavailable, use plant-level data with clear indication
3. PRIORITY 3: Make reasonable estimates based on available plant data

REQUIRED FIELDS:
1. unit_number: "{unit_number}"
2. plant_id: Unique identifier (extract from text or use "0" as fallback)
3. capacity: Unit capacity in MW (look for "Unit {unit_number}" + capacity, or total plant capacity / number of units)
4. capacity_unit: "MW"
5. technology: Plant technology type (Ultra Super Critical, Super Critical, Sub Critical, Combined Cycle, etc.)
6. boiler_type: Boiler specification (if available)
7. commencement_date: Operation start date (yyyy-mm-ddThh:mm:ss.msZ format)

Plant Context: {plant_context.get('plant_name', 'Unknown')} ({plant_context.get('plant_technology', 'Unknown')})

Research Data:
{summaries}

INSTRUCTIONS:
- Extract what's actually available in the data
- If unit-specific capacity not found, divide total plant capacity by number of units
- Use "Not available" only if no relevant information exists
- Provide your best estimate based on available data

Respond ONLY with JSON format:
{{
  "unit_number": "{unit_number}",
  "plant_id": "extracted_or_0",
  "capacity": "estimated_capacity_value",
  "capacity_unit": "MW",
  "technology": "extracted_technology",
  "boiler_type": "extracted_or_Not_available",
  "commencement_date": "extracted_date_or_Not_available"
}}"""

    llm = ChatGoogleGenerativeAI(
        model=reasoning_model,
        temperature=0,
        max_retries=2,
        api_key=os.getenv("GEMINI_API_KEY"),
    )
    
    try:
        result = llm.invoke(prompt)
        content = result.content.strip()
        
        # Use enhanced JSON parsing
        parsed_data = parse_json_response(content, "Basic Info")
        if parsed_data:
            return parsed_data
            
    except Exception as e:
        print(f"❌ Basic info extraction failed: {e}")
        return {
            "unit_number": unit_number,
            "plant_id": "0",
            "capacity": "Not available", 
            "capacity_unit": "MW",
            "technology": "Not available",
            "boiler_type": "Not available",
            "commencement_date": "Not available"
        }


def extract_performance_metrics(summaries: str, unit_number: str, plant_context: dict, reasoning_model: str) -> dict:
    """Stage 2: Extract performance and operational metrics"""
    
    prompt = f"""Extract ONLY the performance and operational metrics for Unit {unit_number}:

EXTRACT ONLY THESE FIELDS:
1. plf: Array of Plant Load Factor data with "value" and "year"
2. PAF: Array of Plant Availability Factor data with "value" and "year"  
3. auxiliary_power_consumed: Array of auxiliary power consumption with "value" and "year"
4. gross_power_generation: Array of power generation with "value" and "year"
5. unit_efficiency: Unit-specific efficiency percentage
6. heat_rate: Station heat rate value
7. heat_rate_unit: Heat rate measurement unit
8. unit_lifetime: Total operational lifetime in years
9. remaining_useful_life: Remaining years of operation

Plant Context: {plant_context.get('plant_name', 'Unknown')} Unit {unit_number}

Research Data:
{summaries}

Respond ONLY with JSON format. Use empty arrays [] for missing time-series data.
Example:
{{
  "plf": [{{"value": "75.5", "year": "2023"}}, {{"value": "78.2", "year": "2022"}}],
  "PAF": [{{"value": "85.3", "year": "2023"}}],
  "auxiliary_power_consumed": [],
  "gross_power_generation": [{{"value": "3500", "year": "2023"}}],
  "unit_efficiency": "42.5",
  "heat_rate": "2150",
  "heat_rate_unit": "kCal/kWh",
  "unit_lifetime": "25",
  "remaining_useful_life": "15"
}}"""

    llm = ChatGoogleGenerativeAI(
        model=reasoning_model,
        temperature=0,
        max_retries=2,
        api_key=os.getenv("GEMINI_API_KEY"),
    )
    
    try:
        result = llm.invoke(prompt)
        content = result.content.strip()
        
        # Use enhanced JSON parsing
        parsed_data = parse_json_response(content, "Performance Metrics")
        if parsed_data:
            return parsed_data
            
    except Exception as e:
        print(f"❌ Performance metrics extraction failed: {e}")
        return {
            "plf": [],
            "PAF": [],
            "auxiliary_power_consumed": [],
            "gross_power_generation": [],
            "unit_efficiency": "Not available",
            "heat_rate": "Not available", 
            "heat_rate_unit": "Not available",
            "unit_lifetime": "Not available",
            "remaining_useful_life": "Not available"
        }


def extract_fuel_and_emissions(summaries: str, unit_number: str, plant_context: dict, reasoning_model: str) -> dict:
    """Stage 3: Extract fuel composition and emission data"""
    
    prompt = f"""Extract ONLY the fuel composition and emission information for Unit {unit_number}:

EXTRACT ONLY THESE FIELDS:
1. fuel_type: Array of fuel objects with "fuel", "type", and "years_percentage"
2. selected_coal_type: Primary coal type used (e.g., Bituminous, Sub-bituminous, Lignite)
3. selected_biomass_type: Biomass type for cofiring (e.g., Wood chips, Agricultural residue)  
4. emission_factor: Array of emission factor data with "value" and "year" (kg CO2e/kWh)
5. gcv_coal: Gross calorific value of coal (kCal/kg)
6. gcv_coal_unit: Unit for coal GCV
7. gcv_biomass: Gross calorific value of biomass
8. gcv_biomass_unit: Unit for biomass GCV
9. efficiency_loss_cofiring: Efficiency reduction from cofiring (%)

Plant Context: {plant_context.get('plant_name', 'Unknown')} Unit {unit_number}

Research Data:
{summaries}

Respond ONLY with JSON format. 
Example:
{{
  "fuel_type": [
    {{"fuel": "Coal", "type": "Bituminous", "years_percentage": {{"2023": "80", "2022": "85"}}}},
    {{"fuel": "Biomass", "type": "Wood chips", "years_percentage": {{"2023": "20", "2022": "15"}}}}
  ],
  "selected_coal_type": "Bituminous",
  "selected_biomass_type": "Wood chips",
  "emission_factor": [{{"value": "0.92", "year": "2023"}}],
  "gcv_coal": "5500",
  "gcv_coal_unit": "kCal/kg",
  "gcv_biomass": "4200", 
  "gcv_biomass_unit": "kCal/kg",
  "efficiency_loss_cofiring": "2.5"
}}"""

    llm = ChatGoogleGenerativeAI(
        model=reasoning_model,
        temperature=0,
        max_retries=2,
        api_key=os.getenv("GEMINI_API_KEY"),
    )
    
    try:
        result = llm.invoke(prompt)
        content = result.content.strip()
        
        # Try to extract JSON from the response
        start_idx = content.find('{')
        end_idx = content.rfind('}') + 1
        
        if start_idx != -1 and end_idx != -1:
            json_str = content[start_idx:end_idx]
            return json.loads(json_str)
        else:
            return json.loads(content)  # Try parsing the whole thing
            
    except Exception as e:
        print(f"❌ Fuel and emissions extraction failed: {e}")
        return {
            "fuel_type": [],
            "selected_coal_type": "Not available",
            "selected_biomass_type": "Not available",
            "emission_factor": [],
            "gcv_coal": "Not available",
            "gcv_coal_unit": "Not available", 
            "gcv_biomass": "Not available",
            "gcv_biomass_unit": "Not available",
            "efficiency_loss_cofiring": "Not available"
        }


def extract_economic_data(summaries: str, unit_number: str, plant_context: dict, reasoning_model: str) -> dict:
    """Stage 4: Extract economic and conversion cost data"""
    
    prompt = f"""Extract ONLY the economic and conversion cost information for Unit {unit_number}:

EXTRACT ONLY THESE FIELDS:
1. capex_required_retrofit: CAPEX for biomass cofiring retrofit
2. capex_required_retrofit_unit: Unit for retrofit CAPEX (e.g., USD million, INR crore)
3. capex_required_renovation_open_cycle: CAPEX for open cycle gas conversion
4. capex_required_renovation_open_cycle_unit: Unit for open cycle CAPEX
5. capex_required_renovation_closed_cycle: CAPEX for closed cycle gas conversion  
6. capex_required_renovation_closed_cycle_unit: Unit for closed cycle CAPEX
7. ppa_details: Array of unit-specific Power Purchase Agreement details

Plant Context: {plant_context.get('plant_name', 'Unknown')} Unit {unit_number}

Research Data:
{summaries}

Respond ONLY with JSON format.
Example:
{{
  "capex_required_retrofit": "50",
  "capex_required_retrofit_unit": "USD million",
  "capex_required_renovation_open_cycle": "200",
  "capex_required_renovation_open_cycle_unit": "USD million",
  "capex_required_renovation_closed_cycle": "350", 
  "capex_required_renovation_closed_cycle_unit": "USD million",
  "ppa_details": [
    {{
      "capacity": "500",
      "unit": "MW",
      "start_date": "2010-01-01",
      "end_date": "2035-12-31",
      "tariff": "3.5",
      "tariff_unit": "INR/kWh"
    }}
  ]
}}"""

    llm = ChatGoogleGenerativeAI(
        model=reasoning_model,
        temperature=0,
        max_retries=2,
        api_key=os.getenv("GEMINI_API_KEY"),
    )
    
    try:
        result = llm.invoke(prompt)
        content = result.content.strip()
        
        # Try to extract JSON from the response
        start_idx = content.find('{')
        end_idx = content.rfind('}') + 1
        
        if start_idx != -1 and end_idx != -1:
            json_str = content[start_idx:end_idx]
            return json.loads(json_str)
        else:
            return json.loads(content)  # Try parsing the whole thing
            
    except Exception as e:
        print(f"❌ Economic data extraction failed: {e}")
        return {
            "capex_required_retrofit": "Not available",
            "capex_required_retrofit_unit": "Not available",
            "capex_required_renovation_open_cycle": "Not available",
            "capex_required_renovation_open_cycle_unit": "Not available",
            "capex_required_renovation_closed_cycle": "Not available",
            "capex_required_renovation_closed_cycle_unit": "Not available", 
            "ppa_details": []
        }


def extract_technical_parameters(summaries: str, unit_number: str, plant_context: dict, reasoning_model: str) -> dict:
    """Stage 5: Extract country-specific technical parameters"""
    
    prompt = f"""Extract ONLY the country-specific technical parameters for Unit {unit_number}:

EXTRACT ONLY THESE FIELDS:
1. gcv_natural_gas: Gross calorific value of natural gas
2. gcv_natural_gas_unit: Unit for gas GCV  
3. open_cycle_gas_turbine_efficency: OCGT efficiency for the country
4. closed_cylce_gas_turbine_efficency: CCGT efficiency for the country
5. combined_cycle_heat_rate: CCGT heat rate for the country
6. open_cycle_heat_rate: OCGT heat rate for the country

Plant Context: {plant_context.get('plant_name', 'Unknown')} Unit {unit_number}
Country: {plant_context.get('country', 'Unknown')}

Research Data:
{summaries}

Respond ONLY with JSON format.
Example:
{{
  "gcv_natural_gas": "9500",
  "gcv_natural_gas_unit": "kCal/m3",
  "open_cycle_gas_turbine_efficency": "35",
  "closed_cylce_gas_turbine_efficency": "55",
  "combined_cycle_heat_rate": "1800",
  "open_cycle_heat_rate": "2800"
}}"""

    llm = ChatGoogleGenerativeAI(
        model=reasoning_model,
        temperature=0,
        max_retries=2,
        api_key=os.getenv("GEMINI_API_KEY"),
    )
    
    try:
        result = llm.invoke(prompt)
        content = result.content.strip()
        
        # Try to extract JSON from the response
        start_idx = content.find('{')
        end_idx = content.rfind('}') + 1
        
        if start_idx != -1 and end_idx != -1:
            json_str = content[start_idx:end_idx]
            return json.loads(json_str)
        else:
            return json.loads(content)  # Try parsing the whole thing
            
    except Exception as e:
        print(f"❌ Technical parameters extraction failed: {e}")
        return {
            "gcv_natural_gas": "Not available",
            "gcv_natural_gas_unit": "Not available",
            "open_cycle_gas_turbine_efficency": "Not available",
            "closed_cylce_gas_turbine_efficency": "Not available",
            "combined_cycle_heat_rate": "Not available",
            "open_cycle_heat_rate": "Not available"
        }


def combine_unit_data(stage_results: list, unit_number: str, plant_context: dict) -> dict:
    """Combine all stage results into final unit data structure"""
    
    # Start with required base structure
    combined_data = {
        "sk": f"scraped#unit#{plant_context.get('plant_technology', 'Unknown')}#{unit_number}#plant#{plant_context.get('plant_id', '0')}",
        "unit_number": unit_number,
        "plant_id": plant_context.get('plant_id', '0'),
        # Default values for fields that might be missing
        "unit": "%",
        "pk": "default null",
        "annual_operational_hours": "default null",
        "blending_percentage": "default null",
        "capex_required_renovation": "default null",
        "capex_required_renovation_unit": "default null",
        "emission_factor_coking_coal": "default null",
        "emission_factor_gas": "default null",
        "emission_factor_of_gas_unit": "default null",
        "emission_factor_unit": "default null",
        "fgds_status": "default null",
        "ramp_down_rate": "default null",
        "ramp_up_rate": "default null"
    }
    
    # Merge all stage results
    for stage_result in stage_results:
        if isinstance(stage_result, dict):
            combined_data.update(stage_result)
    
    return combined_data