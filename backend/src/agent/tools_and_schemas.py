from typing import List, Optional, Any, Dict, Union
from pydantic import BaseModel, Field, field_validator


class SearchQueryList(BaseModel):
    query: List[str] = Field(
        description="A list of search queries to be used for web research."
    )
    rationale: str = Field(
        description="A brief explanation of why these queries are relevant to the research topic."
    )


class Reflection(BaseModel):
    is_sufficient: bool = Field(
        description="Whether the provided summaries are sufficient to answer the user's question."
    )
    knowledge_gap: str = Field(
        description="A description of what information is missing or needs clarification."
    )
    follow_up_queries: List[str] = Field(
        description="A list of follow-up queries to address the knowledge gap."
    )


# Plant-level models
class PPARespondent(BaseModel):
    name: str = Field(
        description="The counterparty's name (utility, trader, corporate buyer, etc.)"
    )
    capacity: str = Field(
        description="The capacity volume contracted by this respondent"
    )
    currency: str = Field(
        description="The currency in which the price is denominated (e.g., 'USD', 'INR')"
    )
    price: str = Field(
        description="The contracted price per unit of energy or capacity"
    )
    price_unit: str = Field(
        description="The basis for the price (e.g., '$/MWh', 'INR/kW-year')"
    )


class PPADetail(BaseModel):
    capacity: str = Field(
        description="The capacity covered by this PPA (typically in MW)"
    )
    capacity_unit: str = Field(
        description="The unit of that capacity (e.g., 'MW', 'kW')"
    )
    start_date: str = Field(
        description="The PPA's commencement date (ISO format, YYYY-MM-DD)"
    )
    end_date: str = Field(
        description="The PPA's termination date (ISO format, YYYY-MM-DD). Typically 25 years from the start date."
    )
    tenure: str = Field(
        description="The numeric duration of the PPA (e.g., 20)"
    )
    tenure_type: str = Field(
        description="The unit for the tenure (e.g., 'Years', 'Months')"
    )
    respondents: List[PPARespondent] = Field(
        description="List of entities that have contracted power from this PPA"
    )


class GridConnectivityProject(BaseModel):
    distance: str = Field(
        description="The distance (e.g., in km) from the substation to that project"
    )


class GridConnectivityDetail(BaseModel):
    substation_name: str = Field(
        description="The official name of the substation"
    )
    substation_type: str = Field(
        description="The classification and voltage level of the substation, including any regional or directional qualifier"
    )
    capacity: str = Field(
        description="The rated capacity of the connection at this substation (e.g., in MW)"
    )
    latitude: str = Field(
        description="The geographic latitude of the substation"
    )
    longitude: str = Field(
        description="The geographic longitude of the substation"
    )
    projects: List[GridConnectivityProject] = Field(
        description="List of projects connected to this substation"
    )


class GridConnectivityMap(BaseModel):
    details: List[GridConnectivityDetail] = Field(
        description="Details of grid connections for this power plant"
    )


class PlantLevelInfo(BaseModel):
    name: str = Field(
        description="The official name of the power plant"
    )
    plant_id: int = Field(
        description="A unique identifier assigned to this plant in your system (integer)"
    )
    plant_type: str = Field(
        description="The technology or fuel type of the plant site"
    )
    lat: str = Field(
        description="The plant's own latitude coordinate"
    )
    long: str = Field(
        description="The plant's own longitude coordinate"
    )
    plant_address: str = Field(
        description="District or city, State, Country"
    )
    ppa_details: List[PPADetail] = Field(
        description="Details of Power Purchase Agreements for this plant"
    )
    grid_connectivity_maps: List[GridConnectivityMap] = Field(
        description="Information about grid connections for this plant"
    )


# Unit-level models for detailed unit processing

class YearlyData(BaseModel):
    value: str = Field(description="The data value for the specific year")
    year: str = Field(description="Year for which the data is reported")

class FuelTypeData(BaseModel):
    fuel: str = Field(default="", description="Fuel source for the plant/unit (Coal, Natural Gas, Biomass, Oil, etc.)")
    type: str = Field(default="", description="Subcategory of the fuel source (e.g., bituminous, sub-bituminous, lignite for coal)")
    years_percentage: Dict[str, str] = Field(
        description="Dictionary mapping year to percentage of fuel used in that year",
        default_factory=dict
    )
    
    @field_validator('years_percentage', mode='before')
    @classmethod
    def validate_years_percentage(cls, v):
        """Handle cases where 'None' string is passed instead of dict"""
        if v is None or v == 'None' or v == '' or v == 'null':
            return {}
        if isinstance(v, str):
            # Try to parse as JSON if it's a string
            try:
                import json
                return json.loads(v)
            except:
                return {}
        if isinstance(v, dict):
            return v
        return {}

class UnitPPARespondent(BaseModel):
    name: str = Field(description="Name of the respondent (utility/buyer)")
    capacity: str = Field(description="Capacity assigned to respondent (in MW)")
    currency: str = Field(description="Currency of the tariff (e.g., USD, INR)")
    price: str = Field(description="Tariff price (per unit energy)")
    price_unit: str = Field(description="Unit for tariff price (e.g., $/kWh)")

class UnitPPADetail(BaseModel):
    capacity: str = Field(description="Contracted capacity under PPA (in MW)")
    capacity_unit: str = Field(description="Unit for capacity, typically 'MW'")
    start_date: str = Field(description="PPA start date (format: yyyy-mm-ddThh:mm:ss.msZ)")
    end_date: str = Field(description="PPA end date (format: yyyy-mm-ddThh:mm:ss.msZ)")
    tenure: str = Field(description="Duration of the PPA in years")
    tenure_type: str = Field(description="Type of tenure (e.g., Fixed, Variable)")
    respondents: List[UnitPPARespondent] = Field(
        description="List of entities that have contracted power from this PPA",
        default_factory=list
    )

class UnitLevelInfo(BaseModel):
    """Complete unit-level information schema based on unit_level.json specification"""
    
    # Basic Unit Identification
    sk: str = Field(default="", description="Unique identifier: scraped#unit#plant_type#unit_id#plant#plant_id")
    unit_number: str = Field(default="", description="Unit number labeling")
    plant_id: str = Field(default="", description="Unique identifier for the plant")
    
    # Technical Specifications
    capacity: str = Field(default="", description="Unit-wise installed capacity of the plant in megawatts (MW)")
    capacity_unit: str = Field(default="MW", description="Unit for installed capacity, typically 'MW'")
    technology: str = Field(default="", description="Coal - Ultra Super Critical, Super Critical, Critical, Sub-critical. Natural Gas - Single/Open Cycle, Combined/Closed Cycle. Biomass - Fluidized Bed Reactor, Direct Combustion, Boiler Conversion")
    boiler_type: str = Field(default="", description="Type of boiler used in the power plant/unit")
    commencement_date: str = Field(default="", description="The date of commercial operation of a specific unit. Format: yyyy-mm-ddThh:mm:ss.msZ")
    remaining_useful_life: str = Field(default="", description="The end-of-life of a specific unit. Format: yyyy-mm-ddThh:mm:ss.msZ")
    unit_lifetime: str = Field(default="", description="Total operational lifetime of the unit in years")
    
    # Performance Metrics (Time-series data)
    plf: List[YearlyData] = Field(
        description="Plant Load Factor (PLF) data by year, represented in %",
        default_factory=list
    )
    PAF: List[YearlyData] = Field(
        description="Plant Availability Factor (PAF) data by year",
        default_factory=list
    )
    auxiliary_power_consumed: List[YearlyData] = Field(
        description="Auxiliary power consumption data by year as percentage of gross generation",
        default_factory=list
    )
    gross_power_generation: List[YearlyData] = Field(
        description="Total energy generated by the unit/plant by year",
        default_factory=list
    )
    emission_factor: List[YearlyData] = Field(
        description="CO2 emissions per kWh generation by year (kg CO2e/kWh)",
        default_factory=list
    )
    
    # Efficiency Metrics
    unit_efficiency: str = Field(default="", description="Unit specific efficiency of the coal power plant unit")
    unit: str = Field(default="%", description="Unit efficiency measurement in percentage (%)")
    heat_rate: str = Field(default="", description="Station Heat Rate - fuel energy required to produce one kWh of electricity")
    heat_rate_unit: str = Field(default="kJ/kWh", description="Unit for heat rate (usually kJ/kWh or kcal/kWh)")
    
    # Fuel Information
    fuel_type: List[FuelTypeData] = Field(
        description="Detailed fuel information including type and yearly usage percentages",
        default_factory=list
    )
    selected_coal_type: str = Field(default="", description="Selected coal type used in the unit")
    selected_biomass_type: str = Field(default="", description="Selected biomass type used for cofiring")
    
    # Country-specific Parameters
    gcv_coal: str = Field(default="", description="Gross calorific value of coal in the country (kCal/kg)")
    gcv_coal_unit: str = Field(default="kCal/kg", description="Unit for GCV of coal (e.g., kCal/kg)")
    gcv_natural_gas: str = Field(default="", description="Gross calorific value of natural gas in the country")
    gcv_natural_gas_unit: str = Field(default="MJ/m³", description="Unit for GCV of natural gas (MJ/m³, MJ/kg, etc.)")
    gcv_biomass: str = Field(default="", description="Gross calorific value of biomass in the country (kCal/kg)")
    gcv_biomass_unit: str = Field(default="kCal/kg", description="Unit for GCV of biomass (e.g., kCal/kg)")
    
    # Technology-specific Efficiency (Country-specific)
    open_cycle_gas_turbine_efficency: str = Field(default="", description="OCGT efficiency for the specific country")
    closed_cylce_gas_turbine_efficency: str = Field(default="", description="CCGT efficiency for the specific country")
    combined_cycle_heat_rate: str = Field(default="", description="Heat rate for CCGT plants in the country")
    open_cycle_heat_rate: str = Field(default="", description="Heat rate for OCGT plants in the country")
    
    # Conversion Economics
    capex_required_retrofit: str = Field(default="", description="CAPEX required to retrofit for biomass cofiring")
    capex_required_retrofit_unit: str = Field(default="Million USD", description="Unit for retrofit CAPEX (Million in local currency)")
    capex_required_renovation_open_cycle: str = Field(default="", description="CAPEX for retrofitting to open cycle natural gas")
    capex_required_renovation_open_cycle_unit: str = Field(default="USD/MW", description="Unit for open cycle renovation CAPEX (e.g., USD/MW)")
    capex_required_renovation_closed_cycle: str = Field(default="", description="CAPEX for retrofitting to closed cycle natural gas")
    capex_required_renovation_closed_cycle_unit: str = Field(default="USD/MW", description="Unit for closed cycle renovation CAPEX (e.g., USD/MW)")
    efficiency_loss_cofiring: str = Field(default="", description="Efficiency reduction (%) from biomass cofiring retrofit")
    
    # Unit-level PPA Details
    ppa_details: List[UnitPPADetail] = Field(
        description="Power Purchase Agreement details specific to this unit",
        default_factory=list
    )


# Organization-level schema based on org_level.json template
class OrganizationLevelInfo(BaseModel):
    pk: str = Field(default="default null", description="Primary key, default null")
    sk: str = Field(default="scraped#org_details", description="Sort key for organization details")
    country_name: str = Field(description="Full name of the country where the organization or plant is located")
    currency_in: str = Field(description="ISO 4217 currency code of that power plant country")
    financial_year: str = Field(description="Fiscal year period in MM-MM format")
    currency_convert_to: str = Field(default="default null", description="Currency to convert to, default null")
    currency_listed: List[str] = Field(default_factory=lambda: ["default null"], description="List of currencies")
    technology_type: List[Dict[str, str]] = Field(
        default_factory=lambda: [
            {"enabled": "true/false", "name": "Renewable Energy"},
            {"enabled": "true/false", "name": "Biomass"},
            {"enabled": "true/false", "name": "SMR"},
            {"enabled": "true/false", "name": "Green Hydrogen Plant"}
        ],
        description="Technology types with enabled status"
    )

# Plant-level schema based on plant_level.json template  
class PlantLevelInfo(BaseModel):
    sk: str = Field(description="Sort key in format: plant#plant_type#Plant_id")
    pk: str = Field(default="default null", description="Primary key, default null")
    annual_operational_hours: int = Field(default=8760, description="Annual operational hours")
    auxiliary_power_consumed: List[YearlyData] = Field(
        default_factory=list,
        description="Auxiliary energy consumed internally by the power plant as percentage of gross energy"
    )
    closure_year: str = Field(default="", description="When the plant is closed down")
    commencement_date: str = Field(
        default="", 
        description="Date of commercial operation in format yyyy-mm-ddThh:mm:ss:ms"
    )
    cuf: List[YearlyData] = Field(
        default_factory=list,
        description="Capacity Utilization Factor values by year"
    )
    gross_power_generation: List[YearlyData] = Field(
        default_factory=list,
        description="Total energy generated by the unit/plant in a Financial Year"
    )
    grid_connectivity_maps: List[GridConnectivityMap] = Field(
        default_factory=list,
        description="Grid connectivity information"
    )
    installed_bess_capacity: str = Field(default="", description="Installed battery capacity of the plant")
    installed_bess_capacity_unit: str = Field(default="MWh", description="Unit for BESS capacity")
    installed_biomass_capacity: str = Field(default="", description="Installed biomass capacity of the plant")
    installed_biomass_capacity_unit: str = Field(default="MW", description="Unit for biomass capacity")
    installed_solar_capacity: str = Field(default="", description="Installed solar capacity of the plant")
    installed_solar_capacity_unit: str = Field(default="MW", description="Unit for solar capacity")
    installed_wind_capacity: str = Field(default="", description="Installed wind capacity of the plant")
    installed_wind_capacity_unit: str = Field(default="MW", description="Unit for wind capacity")
    latitude: str = Field(default="", description="Latitude of the location of the plant")
    longitude: str = Field(default="", description="Longitude of the location of the plant")
    mandatory_closure: str = Field(default="", description="Mandatory closure information")
    name: str = Field(description="Name of the power plant")
    plant_address: str = Field(description="District or city, State, Country")
    plant_id: int = Field(description="Unique identifier starting from 1")
    plant_images: List[str] = Field(default_factory=list, description="List of plant image URLs")
    plant_lifetime: int = Field(default=25, description="Plant lifetime in years")
    plant_type: str = Field(description="Technology or fuel type of the plant site")
    potential_reference: Dict[str, Optional[str]] = Field(
        default_factory=lambda: {"lat": None, "long": None},
        description="Reference location coordinates"
    )
    ppa_details: List[PPADetail] = Field(
        default_factory=list,
        description="Power Purchase Agreement details"
    )
    remaining_useful_life: str = Field(
        default="",
        description="End-of-life date in format yyyy-mm-ddThh:mm:ss:ms"
    )
    total_installed_capacity: str = Field(default="", description="Total installed capacity in MW")
    total_installed_capacity_unit: str = Field(default="MW", description="Unit for total capacity")
    
