"""
DynamoDB Manager for storing organization and plant details

This module handles saving organization and plant information to AWS DynamoDB
for tracking and analytics purposes.
"""

import os
import boto3
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Any
from botocore.exceptions import ClientError


class DynamoDBManager:
    """Manages DynamoDB operations for plant and organization data"""
    
    def __init__(self):
        """Initialize DynamoDB client with AWS credentials from environment"""
        self.table_name = "clem_enablement_details"
        
        # Get AWS credentials from environment
        aws_access_key = os.getenv("AWS_ACCESS_KEY_ID")
        aws_secret_key = os.getenv("AWS_SECRET_ACCESS_KEY")
        aws_region = os.getenv("AWS_DEFAULT_REGION", "ap-south-1")
        
        if not aws_access_key or not aws_secret_key:
            print("⚠️ AWS credentials not found in environment variables")
            self.dynamodb = None
            self.table = None
            return
        
        try:
            # Initialize DynamoDB client
            self.dynamodb = boto3.resource(
                'dynamodb',
                aws_access_key_id=aws_access_key,
                aws_secret_access_key=aws_secret_key,
                region_name=aws_region
            )
            
            # Get or create table
            self.table = self._get_or_create_table()
            print(f"✅ DynamoDB connection established: {self.table_name}")
            
        except Exception as e:
            print(f"❌ Failed to initialize DynamoDB: {e}")
            self.dynamodb = None
            self.table = None
    
    def _get_or_create_table(self):
        """Get existing table or create new one if it doesn't exist"""
        try:
            # Try to get existing table
            table = self.dynamodb.Table(self.table_name)
            table.load()  # This will raise an exception if table doesn't exist
            print(f"✅ Using existing DynamoDB table: {self.table_name}")
            return table
            
        except ClientError as e:
            if e.response['Error']['Code'] == 'ResourceNotFoundException':
                # Table doesn't exist, create it
                print(f"🔧 Creating DynamoDB table: {self.table_name}")
                return self._create_table()
            else:
                raise e
    
    def _create_table(self):
        """Create the DynamoDB table with appropriate schema"""
        try:
            table = self.dynamodb.create_table(
                TableName=self.table_name,
                KeySchema=[
                    {
                        'AttributeName': 'record_id',
                        'KeyType': 'HASH'  # Partition key
                    }
                ],
                AttributeDefinitions=[
                    {
                        'AttributeName': 'record_id',
                        'AttributeType': 'S'
                    }
                ],
                BillingMode='PAY_PER_REQUEST'  # On-demand billing
            )
            
            # Wait for table to be created
            table.wait_until_exists()
            print(f"✅ DynamoDB table created successfully: {self.table_name}")
            return table
            
        except Exception as e:
            print(f"❌ Failed to create DynamoDB table: {e}")
            raise e
    
    def save_organization_details(
        self,
        organization_name: str,
        org_uid: str,
        plant_names: List[str]
    ) -> bool:
        """
        Save organization and plant details to DynamoDB (only 3 required fields)

        Args:
            organization_name: Name of the organization
            org_uid: Organization UID (from database, never generated)
            plant_names: List of plant names under this organization

        Returns:
            bool: True if successful, False otherwise
        """
        if not self.table:
            print("❌ DynamoDB table not available")
            return False

        try:
            # Create record ID using org_uid and timestamp
            record_id = f"org_{org_uid}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

            # Prepare item data - ONLY the 3 required fields
            item_data = {
                'record_id': record_id,  # Primary key for DynamoDB
                'organization_name': organization_name,
                'org_uid': org_uid,
                'plant_names': plant_names
            }

            # Save to DynamoDB
            response = self.table.put_item(Item=item_data)

            print(f"✅ Organization details saved to DynamoDB:")
            print(f"   Organization: {organization_name}")
            print(f"   Org UID: {org_uid}")
            print(f"   Plants: {len(plant_names)} plants")
            print(f"   Plant Names: {', '.join(plant_names[:3])}{'...' if len(plant_names) > 3 else ''}")

            return True

        except Exception as e:
            print(f"❌ Failed to save organization details to DynamoDB: {e}")
            return False
    
    def save_plant_details(
        self,
        plant_name: str,
        organization_name: str,
        org_uid: str,
        plant_uid: str,
        session_id: str,
        plant_data: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        Save individual plant details to DynamoDB
        
        Args:
            plant_name: Name of the plant
            organization_name: Name of the organization
            org_uid: Organization UID
            plant_uid: Plant UID
            session_id: Session ID for tracking
            plant_data: Complete plant data dictionary
            
        Returns:
            bool: True if successful, False otherwise
        """
        if not self.table:
            print("❌ DynamoDB table not available")
            return False
        
        try:
            # Create record ID
            record_id = f"plant_{plant_uid}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            # Prepare item data
            item_data = {
                'record_id': record_id,
                'record_type': 'plant',
                'plant_name': plant_name,
                'organization_name': organization_name,
                'org_uid': org_uid,
                'plant_uid': plant_uid,
                'session_id': session_id,
                'created_at': datetime.now().isoformat(),
                'timestamp': int(datetime.now().timestamp())
            }
            
            # Add plant data if provided
            if plant_data:
                # Extract key plant information
                item_data.update({
                    'plant_type': plant_data.get('plant_type', ''),
                    'total_capacity': plant_data.get('total_installed_capacity', ''),
                    'capacity_unit': plant_data.get('total_installed_capacity_unit', 'MW'),
                    'plant_address': plant_data.get('plant_address', ''),
                    'latitude': plant_data.get('latitude', ''),
                    'longitude': plant_data.get('longitude', ''),
                    'commencement_date': plant_data.get('commencement_date', ''),
                    'sk_value': plant_data.get('sk', ''),
                    'plant_data_summary': str(plant_data)[:1000]  # Store summary (truncated)
                })
            
            # Save to DynamoDB
            response = self.table.put_item(Item=item_data)
            
            print(f"✅ Plant details saved to DynamoDB:")
            print(f"   Record ID: {record_id}")
            print(f"   Plant: {plant_name}")
            print(f"   Organization: {organization_name}")
            print(f"   Plant UID: {plant_uid}")
            
            return True
            
        except Exception as e:
            print(f"❌ Failed to save plant details to DynamoDB: {e}")
            return False
    
    def get_organization_records(self, org_uid: str) -> List[Dict[str, Any]]:
        """
        Get all records for a specific organization
        
        Args:
            org_uid: Organization UID to search for
            
        Returns:
            List of records for the organization
        """
        if not self.table:
            return []
        
        try:
            # Scan table for records with matching org_uid
            response = self.table.scan(
                FilterExpression=boto3.dynamodb.conditions.Attr('org_uid').eq(org_uid)
            )
            
            return response.get('Items', [])
            
        except Exception as e:
            print(f"❌ Failed to get organization records: {e}")
            return []
    
    def is_available(self) -> bool:
        """Check if DynamoDB is available and configured"""
        return self.table is not None


# Global instance
_dynamodb_manager = None

def get_dynamodb_manager() -> DynamoDBManager:
    """Get global DynamoDB manager instance"""
    global _dynamodb_manager
    if _dynamodb_manager is None:
        _dynamodb_manager = DynamoDBManager()
    return _dynamodb_manager
