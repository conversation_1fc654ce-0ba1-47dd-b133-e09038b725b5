"""
JSON S3 Storage Module for Power Plant Research Data

This module handles uploading JSON data at different processing levels
(organization, plant, unit) to S3 with hierarchical folder structure.

Hierarchical Structure: Country/OrgUID/PlantUID/filename.json
"""

import os
import json
import re
import boto3
from datetime import datetime
from typing import Dict, Any, Optional
from dotenv import load_dotenv

load_dotenv()

# S3 Configuration - Use S3-specific credentials
S3_AWS_ACCESS_KEY = os.getenv('S3_AWS_ACCESS_KEY_ID')
S3_AWS_SECRET_KEY = os.getenv('S3_AWS_SECRET_ACCESS_KEY')
AWS_REGION = os.getenv('AWS_REGION', 'ap-south-1')
S3_BUCKET = 'clem-enablement-plants'

print(f"🔧 S3 Configuration:")
print(f"   - S3 Access Key: {S3_AWS_ACCESS_KEY[:8] + '...' if S3_AWS_ACCESS_KEY else 'NOT SET'}")
print(f"   - S3 Region: {AWS_REGION}")
print(f"   - S3 Bucket: {S3_BUCKET}")

def sanitize_plant_name(plant_name: str) -> str:
    """
    Convert power plant name to S3-safe folder name.

    Examples:
        "Jhajjar Power Plant" → "Jhajjar_Power_Plant"
        "NTPC Dadri (Stage-II)" → "NTPC_Dadri_Stage_II"
        "Adani Mundra Power Station" → "Adani_Mundra_Power_Station"
    """
    if not plant_name:
        return "Unknown_Plant"

    # Remove special characters except spaces and hyphens, replace & with _and_
    cleaned = plant_name.replace('&', '_and_')
    cleaned = re.sub(r'[^\w\s-]', '', cleaned)

    # Replace spaces and hyphens with underscores
    sanitized = cleaned.replace(' ', '_').replace('-', '_')

    # Remove multiple consecutive underscores
    sanitized = re.sub(r'_+', '_', sanitized)

    # Remove leading/trailing underscores
    sanitized = sanitized.strip('_')

    return sanitized if sanitized else "Unknown_Plant"

def get_country_folder_name(country: str) -> str:
    """Convert country name to 3-letter ISO country code for S3 folder naming"""
    if not country or country.lower() in ['unknown', 'not available', '']:
        return "UNK"  # Unknown country code

    # Country name to ISO 3-letter code mapping
    country_codes = {
        # Major countries
        'australia': 'AUS',
        'united states': 'USA',
        'united states of america': 'USA',
        'usa': 'USA',
        'canada': 'CAN',
        'united kingdom': 'GBR',
        'uk': 'GBR',
        'britain': 'GBR',
        'great britain': 'GBR',
        'germany': 'DEU',
        'france': 'FRA',
        'italy': 'ITA',
        'spain': 'ESP',
        'netherlands': 'NLD',
        'belgium': 'BEL',
        'switzerland': 'CHE',
        'austria': 'AUT',
        'sweden': 'SWE',
        'norway': 'NOR',
        'denmark': 'DNK',
        'finland': 'FIN',
        'poland': 'POL',
        'czech republic': 'CZE',
        'hungary': 'HUN',
        'romania': 'ROU',
        'bulgaria': 'BGR',
        'greece': 'GRC',
        'portugal': 'PRT',
        'ireland': 'IRL',
        'iceland': 'ISL',
        'luxembourg': 'LUX',
        'malta': 'MLT',
        'cyprus': 'CYP',
        'estonia': 'EST',
        'latvia': 'LVA',
        'lithuania': 'LTU',
        'slovakia': 'SVK',
        'slovenia': 'SVN',
        'croatia': 'HRV',
        'serbia': 'SRB',
        'bosnia and herzegovina': 'BIH',
        'montenegro': 'MNE',
        'north macedonia': 'MKD',
        'albania': 'ALB',
        'moldova': 'MDA',
        'ukraine': 'UKR',
        'belarus': 'BLR',
        'russia': 'RUS',
        'russian federation': 'RUS',

        # Asia-Pacific
        'china': 'CHN',
        'japan': 'JPN',
        'south korea': 'KOR',
        'korea': 'KOR',
        'india': 'IND',
        'indonesia': 'IDN',
        'thailand': 'THA',
        'vietnam': 'VNM',
        'philippines': 'PHL',
        'malaysia': 'MYS',
        'singapore': 'SGP',
        'taiwan': 'TWN',
        'hong kong': 'HKG',
        'new zealand': 'NZL',
        'pakistan': 'PAK',
        'bangladesh': 'BGD',
        'sri lanka': 'LKA',
        'myanmar': 'MMR',
        'cambodia': 'KHM',
        'laos': 'LAO',
        'brunei': 'BRN',
        'mongolia': 'MNG',
        'nepal': 'NPL',
        'bhutan': 'BTN',
        'maldives': 'MDV',

        # Middle East & Africa
        'saudi arabia': 'SAU',
        'united arab emirates': 'ARE',
        'uae': 'ARE',
        'qatar': 'QAT',
        'kuwait': 'KWT',
        'bahrain': 'BHR',
        'oman': 'OMN',
        'israel': 'ISR',
        'turkey': 'TUR',
        'iran': 'IRN',
        'iraq': 'IRQ',
        'syria': 'SYR',
        'lebanon': 'LBN',
        'jordan': 'JOR',
        'egypt': 'EGY',
        'south africa': 'ZAF',
        'nigeria': 'NGA',
        'kenya': 'KEN',
        'ethiopia': 'ETH',
        'ghana': 'GHA',
        'morocco': 'MAR',
        'algeria': 'DZA',
        'tunisia': 'TUN',
        'libya': 'LBY',
        'sudan': 'SDN',
        'uganda': 'UGA',
        'tanzania': 'TZA',
        'rwanda': 'RWA',
        'zambia': 'ZMB',
        'zimbabwe': 'ZWE',
        'botswana': 'BWA',
        'namibia': 'NAM',
        'mozambique': 'MOZ',
        'madagascar': 'MDG',
        'mauritius': 'MUS',

        # Americas
        'brazil': 'BRA',
        'mexico': 'MEX',
        'argentina': 'ARG',
        'chile': 'CHL',
        'colombia': 'COL',
        'peru': 'PER',
        'venezuela': 'VEN',
        'ecuador': 'ECU',
        'bolivia': 'BOL',
        'paraguay': 'PRY',
        'uruguay': 'URY',
        'guyana': 'GUY',
        'suriname': 'SUR',
        'costa rica': 'CRI',
        'panama': 'PAN',
        'guatemala': 'GTM',
        'honduras': 'HND',
        'el salvador': 'SLV',
        'nicaragua': 'NIC',
        'belize': 'BLZ',
        'jamaica': 'JAM',
        'cuba': 'CUB',
        'dominican republic': 'DOM',
        'haiti': 'HTI',
        'trinidad and tobago': 'TTO',
        'barbados': 'BRB',
        'bahamas': 'BHS'
    }

    # Normalize country name for lookup
    country_normalized = country.lower().strip()

    # Direct lookup
    if country_normalized in country_codes:
        return country_codes[country_normalized]

    # Try partial matches for common variations
    for country_name, code in country_codes.items():
        if country_normalized in country_name or country_name in country_normalized:
            return code

    # If no match found, create a 3-letter code from the country name
    # Take first 3 letters and uppercase
    fallback_code = re.sub(r'[^\w]', '', country)[:3].upper()
    if len(fallback_code) < 3:
        fallback_code = (fallback_code + 'XXX')[:3]

    print(f"⚠️ Unknown country '{country}', using fallback code: {fallback_code}")
    return fallback_code

def upload_hierarchical_json_to_s3(
    json_data: Dict[Any, Any],
    country: str,
    org_uid: str,
    plant_uid: str,
    filename: str,  # This parameter is now ignored - filenames are auto-generated
    session_id: str = "unknown"
) -> Optional[str]:
    """
    Upload JSON data to S3 with hierarchical folder structure.

    New Structure:
    - Organization level: Country_Code/org_id/org_details.json
    - Plant level: Country_Code/org_id/plant_id/{sk_value}.json (where sk_value comes from JSON)

    Args:
        json_data: Dictionary containing the data to upload
        country: Country name (converted to 3-letter ISO code)
        org_uid: Organization UID for folder structure
        plant_uid: Plant UID for folder structure (empty for org-level files)
        filename: Ignored - filenames are now auto-generated based on content
        session_id: Session ID for logging

    Returns:
        S3 URL of uploaded file, or None if upload failed
    """
    try:
        # Initialize S3 client
        s3_client = boto3.client(
            's3',
            aws_access_key_id=S3_AWS_ACCESS_KEY,
            aws_secret_access_key=S3_AWS_SECRET_KEY,
            region_name=AWS_REGION
        )

        # Add metadata to JSON
        enhanced_data = {
            **json_data,
            "metadata": {
                "uploaded_at": datetime.utcnow().isoformat() + "Z",
                "session_id": session_id,
                "country": country,
                "org_uid": org_uid,
                "plant_uid": plant_uid,
                "file_type": filename.replace('.json', ''),
                "bucket": S3_BUCKET
            }
        }

        # Convert to JSON string
        json_string = json.dumps(enhanced_data, indent=2, ensure_ascii=False)

        # Create hierarchical S3 key path according to new format
        country_folder = get_country_folder_name(country)

        if plant_uid:
            # Plant level: Country/org_id/plant_id/{sk_value}.json
            # Extract SK value from JSON data for filename
            sk_value = json_data.get("sk", "plant_unknown")
            filename = f"{sk_value}.json"
            s3_key = f"{country_folder}/{org_uid}/{plant_uid}/{filename}"
            print(f"[Session {session_id}] 🏭 Plant level S3 key: {s3_key}")
            print(f"[Session {session_id}] 🏭 Using SK value as filename: {filename}")
        else:
            # Organization level: Country/org_id/org_details.json
            s3_key = f"{country_folder}/{org_uid}/org_details.json"
            print(f"[Session {session_id}] 🏢 Organization level S3 key: {s3_key}")

        # Upload to S3
        s3_client.put_object(
            Bucket=S3_BUCKET,
            Key=s3_key,
            Body=json_string,
            ContentType='application/json',
            ContentEncoding='utf-8'
        )

        # Generate S3 URL
        s3_url = f"https://{S3_BUCKET}.s3.amazonaws.com/{s3_key}"

        print(f"[Session {session_id}] ✅ Hierarchical JSON uploaded: {s3_url}")
        print(f"[Session {session_id}] 📁 S3 Path: {s3_key}")
        return s3_url

    except Exception as e:
        print(f"[Session {session_id}] ❌ Failed to upload hierarchical {filename}: {str(e)}")
        return None
def store_organization_data(
    org_data: Dict[Any, Any],
    plant_name: str,
    session_id: str = "unknown",
    org_uid: str = None
) -> Optional[str]:
    """
    Store organization-level data to S3.

    Args:
        org_data: Organization data dictionary
        plant_name: Original plant name from user query
        session_id: Session ID for tracking
        org_uid: Organization UID (primary key)

    Returns:
        S3 URL of uploaded file
    """
    # CRITICAL: Fetch pk from database for AGI integration
    print(f"[Session {session_id}] 🔍 DEBUG: org_uid = '{org_uid}'")
    print(f"[Session {session_id}] 🔍 DEBUG: org_data keys = {list(org_data.keys())}")
    print(f"[Session {session_id}] 🔍 DEBUG: current pk = '{org_data.get('pk', 'NOT_FOUND')}'")

    if org_uid:
        org_data["org_uid"] = org_uid

        # CRITICAL: Check if this org_uid exists in database and fetch pk from there
        try:
            from agent.database_manager import get_database_manager
            db_manager = get_database_manager()

            # Check if any plant with this org_uid exists in database
            plants_with_org_uid = db_manager.get_plants_by_org_uid(org_uid)

            if plants_with_org_uid:
                # Use org_uid from database as pk (AGI integration)
                old_pk = org_data.get("pk", "NOT_FOUND")
                org_data["pk"] = org_uid
                print(f"[Session {session_id}] ✅ FETCHED pk from database: '{old_pk}' → '{org_uid}'")
                print(f"[Session {session_id}] 🔑 AGI Integration: Using org_uid as pk from database")
            else:
                # New organization - use org_uid as pk
                old_pk = org_data.get("pk", "NOT_FOUND")
                org_data["pk"] = org_uid
                print(f"[Session {session_id}] ✅ NEW ORG: Set pk field: '{old_pk}' → '{org_uid}'")
                print(f"[Session {session_id}] 🔑 Added UID to organization data: {org_uid}")

        except Exception as e:
            print(f"[Session {session_id}] ❌ Error fetching pk from database: {e}")
            # Fallback: use org_uid as pk
            old_pk = org_data.get("pk", "NOT_FOUND")
            org_data["pk"] = org_uid
            print(f"[Session {session_id}] 🔧 FALLBACK: Set pk field: '{old_pk}' → '{org_uid}'")

    else:
        print(f"[Session {session_id}] ❌ No org_uid provided to store_organization_data")
        print(f"[Session {session_id}] 🔍 EMERGENCY FIX: Attempting to generate UID from org data...")

        # EMERGENCY FIX: Try to generate UID from organization data if not provided
        org_name = org_data.get("organization_name", "")
        country = org_data.get("country_name", "")

        if org_name and country:
            try:
                from agent.database_manager import get_database_manager
                db_manager = get_database_manager()
                emergency_uid = db_manager.generate_org_uid(org_name, country)

                org_data["org_uid"] = emergency_uid
                old_pk = org_data.get("pk", "NOT_FOUND")
                org_data["pk"] = emergency_uid
                print(f"[Session {session_id}] 🚨 EMERGENCY UID GENERATED: '{emergency_uid}'")
                print(f"[Session {session_id}] ✅ Set pk field: '{old_pk}' → '{emergency_uid}'")
            except Exception as e:
                print(f"[Session {session_id}] ❌ Emergency UID generation failed: {e}")
                # Last resort: set pk to null instead of "default null"
                org_data["pk"] = None
                print(f"[Session {session_id}] 🔧 Set pk to null as last resort")
        else:
            print(f"[Session {session_id}] ❌ Cannot generate emergency UID: missing org_name or country")
            # Last resort: set pk to null instead of "default null"
            org_data["pk"] = None
            print(f"[Session {session_id}] 🔧 Set pk to null as last resort")

    # CRITICAL FIX: Enforce fixed values for off_peak_hours and peak_hours right before storage
    # This ensures they are NEVER null regardless of what the LLM generated
    #org_data["off_peak_hours"] = 0.466
    #org_data["peak_hours"] = 0.9
    #print(f"[Session {session_id}] 🔧 FINAL ENFORCEMENT: off_peak_hours=0.466, peak_hours=0.9")

    # Get country from database first, then fallback to organization data
    country = "Unknown"
    try:
        from agent.database_manager import get_database_manager
        db_manager = get_database_manager()
        plant_info = db_manager.check_plant_exists(plant_name)
        if plant_info and plant_info.get("country"):
            country = plant_info["country"]
            print(f"[Session {session_id}] 🌍 Retrieved country from database: {country}")
        else:
            # Fallback to organization data
            country = org_data.get("country_name", "Unknown")
            print(f"[Session {session_id}] 🔄 Using country from org data: {country}")
    except Exception as e:
        print(f"[Session {session_id}] ⚠️ Could not retrieve country from database: {e}")
        country = org_data.get("country_name", "Unknown")
    if country in ["Unknown", "Not available", "", None]:
        # Try to get country from database using plant name
        try:
            from agent.database_manager import get_database_manager
            db_manager = get_database_manager()
            plant_info = db_manager.check_plant_exists(plant_name)
            if plant_info and plant_info.get("country"):
                country = plant_info["country"]
                print(f"[Session {session_id}] 🌍 Retrieved country from database: {country}")
        except Exception as e:
            print(f"[Session {session_id}] ⚠️ Could not retrieve country from database: {e}")
            country = "Unknown"

    # Get org_uid for folder structure (filename will be org_id.json automatically)
    final_org_uid = org_data.get("org_uid", "UNKNOWN_ORG")

    print(f"[Session {session_id}] 🏢 Storing organization data hierarchically")
    print(f"[Session {session_id}] 🌍 Country: {country}")
    print(f"[Session {session_id}] 🏢 Org UID: {final_org_uid}")
    print(f"[Session {session_id}] 📄 Filename: org_details.json (organization level)")

    # Pass empty plant_uid to indicate organization level storage
    return upload_hierarchical_json_to_s3(org_data, country, final_org_uid, "", "", session_id)

def store_plant_data(
    plant_data: Dict[Any, Any],
    plant_name: str,
    session_id: str = "unknown",
    org_uid: str = None,
    plant_uid: str = None
) -> Optional[str]:
    """
    Store plant-level data to S3.

    Args:
        plant_data: Plant data dictionary
        plant_name: Original plant name from user query
        session_id: Session ID for tracking
        org_uid: Organization UID
        plant_uid: Plant UID (primary key for plant level)

    Returns:
        S3 URL of uploaded file
    """
    # CRITICAL: Always fetch plant data from database first
    print(f"[Session {session_id}] 🔍 DEBUG: Fetching plant data from database for: {plant_name}")
    print(f"[Session {session_id}] 🔍 DEBUG: Provided org_uid = '{org_uid}'")
    print(f"[Session {session_id}] 🔍 DEBUG: Provided plant_uid = '{plant_uid}'")

    # Initialize country variable to avoid UnboundLocalError
    country = "Unknown"

    # Always fetch from database to get the latest org_uid, plant_uid, and country
    try:
        from agent.database_manager import get_database_manager
        db_manager = get_database_manager()

        # Check if this plant exists in database
        plant_info = db_manager.check_plant_exists(plant_name)

        # If not found, try to find similar plant names (remove suffixes like _Plant_1)
        if not plant_info and "_Plant_" in plant_name:
            base_plant_name = plant_name.split("_Plant_")[0]
            print(f"[Session {session_id}] 🔍 Plant not found, trying base name: {base_plant_name}")
            plant_info = db_manager.check_plant_exists(base_plant_name)

        # If still not found and we have org_uid, try to find any plant with this org_uid
        if not plant_info and org_uid:
            print(f"[Session {session_id}] 🔍 Plant not found by name, searching by org_uid: {org_uid}")
            try:
                plants_with_org_uid = db_manager.get_plants_by_org_uid(org_uid)
                if plants_with_org_uid:
                    # Use the first plant found with this org_uid for country information
                    plant_info = {
                        "org_uid": plants_with_org_uid[0].org_uid,
                        "plant_uid": plants_with_org_uid[0].plant_uid,
                        "country": plants_with_org_uid[0].country,
                        "plant_name": plants_with_org_uid[0].plant_name
                    }
                    print(f"[Session {session_id}] 🔍 Found plant by org_uid: {plant_info['plant_name']} in {plant_info['country']}")
            except Exception as e:
                print(f"[Session {session_id}] ⚠️ Error searching by org_uid: {e}")

        if plant_info:
            # Use database values (most reliable source)
            db_org_uid = plant_info.get("org_uid")
            db_plant_uid = plant_info.get("plant_uid")
            db_country = plant_info.get("country", "Unknown")

            print(f"[Session {session_id}] ✅ Plant found in database:")
            print(f"[Session {session_id}]    Org UID: {db_org_uid}")
            print(f"[Session {session_id}]    Plant UID: {db_plant_uid}")
            print(f"[Session {session_id}]    Country: {db_country}")

            # Override parameters with database values
            org_uid = db_org_uid
            plant_uid = db_plant_uid
            country = db_country

            # Set plant data fields
            plant_data["org_uid"] = org_uid
            if plant_uid:
                plant_data["plant_uid"] = plant_uid
                plant_data["pk"] = plant_uid  # Use plant_uid as primary key
                print(f"[Session {session_id}] 🔑 Using plant_uid as pk: {plant_uid}")
            else:
                plant_data["pk"] = org_uid  # Fallback to org_uid
                print(f"[Session {session_id}] ⚠️ No plant_uid in database, using org_uid as pk: {org_uid}")

            print(f"[Session {session_id}] 🔑 Using database values for S3 storage")

        else:
            print(f"[Session {session_id}] ⚠️ Plant not found in database: {plant_name}")
            print(f"[Session {session_id}] 🔄 Using provided parameters as fallback")

            # Keep default country value since plant not found in database
            country = "Unknown"

            # Use provided parameters as fallback
            if org_uid:
                plant_data["org_uid"] = org_uid
            if plant_uid:
                plant_data["plant_uid"] = plant_uid
                plant_data["pk"] = plant_uid
                print(f"[Session {session_id}] 🔑 Using provided plant_uid as pk: {plant_uid}")
            elif org_uid:
                # Use org_uid as pk if no plant_uid available
                plant_data["pk"] = org_uid
                print(f"[Session {session_id}] ⚠️ No plant_uid, using provided org_uid as pk: {org_uid}")
            else:
                print(f"[Session {session_id}] ❌ No plant_uid or org_uid available")

    except Exception as e:
        print(f"[Session {session_id}] ❌ Error fetching from database: {e}")
        print(f"[Session {session_id}] 🔄 Using provided parameters as fallback")

        # Keep default country value since database fetch failed
        country = "Unknown"

        # Use provided parameters as fallback
        if org_uid:
            plant_data["org_uid"] = org_uid
        if plant_uid:
            plant_data["plant_uid"] = plant_uid
            plant_data["pk"] = plant_uid
        elif org_uid:
            plant_data["pk"] = org_uid

    # Ensure we have valid values (country should already be set from database above)
    if not country:
        country = "Unknown"
        print(f"[Session {session_id}] ⚠️ country was None/empty, using fallback: {country}")

    if not org_uid:  # Handles both None and empty string
        org_uid = "UNKNOWN_ORG"
        print(f"[Session {session_id}] ⚠️ org_uid was None/empty, using fallback: {org_uid}")

    if not plant_uid:  # Handles both None and empty string
        plant_uid = "UNKNOWN_PLANT"
        print(f"[Session {session_id}] ⚠️ plant_uid was None/empty, using fallback: {plant_uid}")

    # Get SK value for filename
    sk_value = plant_data.get("sk", "plant_unknown")
    filename = f"{sk_value}.json"

    print(f"[Session {session_id}] 🏭 Storing plant data hierarchically")
    print(f"[Session {session_id}] 🌍 Country: {country}")
    print(f"[Session {session_id}] 🏢 Org UID: {org_uid}")
    print(f"[Session {session_id}] 🏭 Plant UID: {plant_uid}")
    print(f"[Session {session_id}] 📄 Filename: {filename} (using SK value)")

    # Pass plant_uid to indicate plant level storage (filename will be plant_sk.json automatically)
    return upload_hierarchical_json_to_s3(plant_data, country, org_uid, plant_uid, "", session_id)





def get_plant_s3_urls(plant_name: str, session_id: str = "unknown") -> Dict[str, Any]:
    """
    Generate hierarchical S3 URLs for all expected files of a plant (for state tracking).

    Args:
        plant_name: Original plant name from user query
        session_id: Session ID for tracking

    Returns:
        Dictionary with hierarchical URL structure for state management
    """
    # Get plant metadata from database for hierarchical structure
    try:
        from agent.database_manager import get_database_manager
        db_manager = get_database_manager()
        plant_info = db_manager.check_plant_exists(plant_name)

        if plant_info:
            country = get_country_folder_name(plant_info.get("country", "Unknown"))
            org_uid = plant_info.get("org_uid", "UNKNOWN_ORG")
            plant_uid = plant_info.get("plant_uid", "UNKNOWN_PLANT")

            base_url = f"https://{S3_BUCKET}.s3.amazonaws.com/{country}/{org_uid}"
            plant_base_url = f"{base_url}/{plant_uid}"

            return {
                "plant_name": plant_name,
                "country": country,
                "org_uid": org_uid,
                "plant_uid": plant_uid,
                "base_url": base_url,
                "plant_base_url": plant_base_url,
                "organization": f"{base_url}/org_details.json",
                "plant": f"{plant_base_url}/{{sk_value}}.json"  # Will be updated with actual SK value
            }
        else:
            print(f"[Session {session_id}] ⚠️ Plant not found in database: {plant_name}")
            return {"error": "Plant not found in database"}

    except Exception as e:
        print(f"[Session {session_id}] ❌ Error generating hierarchical S3 URLs: {e}")
        return {"error": str(e)}
def check_s3_connection(session_id: str = "test") -> bool:
    """
    Test S3 connection and credentials.

    Returns:
        True if connection successful, False otherwise
    """
    try:
        s3_client = boto3.client(
            's3',
            aws_access_key_id=S3_AWS_ACCESS_KEY,
            aws_secret_access_key=S3_AWS_SECRET_KEY,
            region_name=AWS_REGION
        )

        # Try to list objects (this will fail if credentials are wrong)
        s3_client.head_bucket(Bucket=S3_BUCKET)
        print(f"[Session {session_id}] ✅ S3 connection successful to bucket: {S3_BUCKET}")
        return True

    except Exception as e:
        print(f"[Session {session_id}] ❌ S3 connection failed: {str(e)}")
        return False

# For testing/debugging
if __name__ == "__main__":
    # Test sanitization
    test_names = [
        "Jhajjar Power Plant",
        "NTPC Dadri (Stage-II)",
        "Adani Mundra Power Station",
        "Tata Power Plant - Unit 1&2"
    ]

    print("Testing plant name sanitization:")
    for name in test_names:
        sanitized = sanitize_plant_name(name)
        print(f"'{name}' → '{sanitized}'")

    # Test S3 connection
    print("\nTesting S3 connection:")
    check_s3_connection()