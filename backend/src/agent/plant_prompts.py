"""
Plant Technology-Specific Prompts

This module contains detailed prompts for different renewable energy technologies.
Each technology has specific data requirements and formats.
"""

def get_solar_plant_prompt(plant_name: str, country: str) -> str:
    """
    Get detailed data extraction prompt for solar power plants
    
    Args:
        plant_name: Name of the solar plant
        country: Country where the plant is located
        
    Returns:
        Formatted prompt string for solar plant data extraction
    """
    return f"""
Organisation details:

Please provide the following for the {plant_name} in {country}:
1. Ownership status: Public or private
2. Owner organization name
3. Total number of operational power plants owned by that same organization (globally)
4. Country's state in which the plant is situated (e.g., Texas)
5. Financial period of the country in which plant is located: (e.g. april 1- March 31)

Plant details:

Please provide the following for the {plant_name} in {country}:
1. Plant official name
2. Primary renewable technology (e.g. solar PV, wind, hybrid solar + battery, etc.)
3. Total nameplate capacity (MW) (e.g. 255 MW)
4. Plant address: e.g. Andrews county, Texas, USA
5. Site latitude (decimal degrees): -33.456
6. Site longitude (decimal degrees): 45.384
7. Confirm whether the Plant was commissioned in multiple phases or in a single phase. Respond with 'Yes' or 'No'
8. Please provide: i) The overall Commercial Operation Date (format: YYYY/MM/DD) ii) If the plant was commissioned in multiple phases, list each phase by name (e.g., Phase 1, Phase2, etc.) alongside its individual Commercial Operation Date (format: YYYY/MM/DD).

PPA details:

Please provide the PPA details for the {plant_name} in {country}, using the exact format below and filling in each field with the correct information:
1. Number of PPA plant have and following details for each
i. Contracted capacity: e.g., 500 MW
ii. Start date (YYYY/MM/DD): e.g., 2011/09/08
iii. End date (YYYY/MM/DD): e.g., 2029/12/01
iv. Tenure: e.g., 25 years
v. Respondent name: e.g., Eskom
vi. Capacity contracted with each respondent: e.g., 500 MW
vii. Tariff price: e.g., 10
viii. Tariff currency: e.g., AUD
ix. Tariff unit: e.g., AUD/kWh

Power generation:

Please provide for the {plant_name} in {country}:
1. CUF of the solar plant (e.g 0.22, for each reporting year: 2019–20, 2020–21, 2021–22, 2022–23, 2023–24, find at least last three years)
2. Annual net generation in MWh for each reporting year: 2019–20, 2020–21, 2021–22, 2022–23, 2023–24 (Note: if the annual generation is not available use CUF to extract annual power generation)
3. AUX percentage (self consumption by plant) e.g. 0.5% [Note: if not available take as 0.5%]

Grid connected details:

Please provide the grid connectivity details for the {plant_name} in {country}, using the exact format below and filling in each field with the correct information:
1. Name of the substation to which the power station is connected: e.g., Bluewaters Substation
2. Latitude of the substation (decimal degrees): e.g., -35.014
3. Longitude of the substation (decimal degrees): e.g., 158.030
4. Line capacity of the substation: e.g., 400 kV
5. Linear distance of the substation from the power station: e.g., 26 km
"""


def get_wind_plant_prompt(plant_name: str, country: str) -> str:
    """
    Get detailed data extraction prompt for wind power plants

    Args:
        plant_name: Name of the wind plant
        country: Country where the plant is located

    Returns:
        Formatted prompt string for wind plant data extraction
    """
    return f"""
Organisation details:

Please provide the following for the {plant_name} in {country}:
1. Ownership status: Public or private
2. Owner organization name
3. Total number of operational power plants owned by that same organization (globally)
4. Country's state in which the plant is situated (e.g., Texas)
5. Financial period of the country in which plant is located: (e.g. april 1- March 31)

Plant details:

Please provide the following for the {plant_name} in {country}:
1. Plant official name
2. Primary renewable technology (e.g. solar PV, wind, hybrid solar + battery, etc.)
3. Total nameplate capacity (MW) (e.g. 255 MW)
4. Plant address: e.g. Andrews county, Texas, USA
5. Site latitude (decimal degrees): -33.456
6. Site longitude (decimal degrees): 45.384
7. Confirm whether the Plant was commissioned in multiple phases or in a single phase. Respond with 'Yes' or 'No'
8. Please provide: i) The overall Commercial Operation Date (format: YYYY/MM/DD) ii) If the plant was commissioned in multiple phases, list each phase by name (e.g., Phase 1, Phase2, etc.) alongside its individual Commercial Operation Date (format: YYYY/MM/DD).

PPA details:

Please provide the PPA details for the {plant_name} in {country}, using the exact format below and filling in each field with the correct information:
1. Number of PPA plant have and following details for each
i. Contracted capacity: e.g., 500 MW
ii. Start date (YYYY/MM/DD): e.g., 2011/09/08
iii. End date (YYYY/MM/DD): e.g., 2029/12/01
iv. Tenure: e.g., 25 years
v. Respondent name: e.g., Eskom
vi. Capacity contracted with each respondent: e.g., 500 MW
vii. Tariff price: e.g., 10
viii. Tariff currency: e.g., AUD
ix. Tariff unit: e.g., AUD/kWh

Power generation:

Please provide for the {plant_name} in {country}:
1. CUF of the Wind plant (e.g 0.22, for each reporting year: 2019–20, 2020–21, 2021–22, 2022–23, 2023–24, find at least last three years)
2. Annual net generation in MWh for each reporting year: 2019–20, 2020–21, 2021–22, 2022–23, 2023–24 (Note: if the annual generation is not available use CUF to extract annual power generation)
3. AUX percentage (self consumption by plant) e.g. 0.5% [Note: if not available take as 0.5%]

Grid connected details:

Please provide the grid connectivity details for the {plant_name} in {country}, using the exact format below and filling in each field with the correct information:
1. Name of the substation to which the power station is connected: e.g., Bluewaters Substation
2. Latitude of the substation (decimal degrees): e.g., -35.014
3. Longitude of the substation (decimal degrees): e.g., 158.030
4. Line capacity of the substation: e.g., 400 kV
5. Linear distance of the substation from the power station: e.g., 26 km
"""


def get_hybrid_plant_prompt(plant_name: str, country: str) -> str:
    """
    Get detailed data extraction prompt for hybrid power plants (solar + battery, wind + solar, etc.)
    
    Args:
        plant_name: Name of the hybrid plant
        country: Country where the plant is located
        
    Returns:
        Formatted prompt string for hybrid plant data extraction
    """
    return f"""
Organisation details:

Please provide the following for the {plant_name} in {country}:
1. Ownership status: Public or private
2. Owner organization name
3. Total number of operational power plants owned by that same organization (globally)
4. Country's state in which the plant is situated (e.g., Texas)
5. Financial period of the country in which plant is located: (e.g. april 1- March 31)

Plant details:

Please provide the following for the {plant_name} in {country}:
1. Plant official name
2. Primary renewable technology (e.g. solar PV, wind, hybrid solar + battery, etc.)
3. Total nameplate capacity (MW) (e.g. 255 MW)
4. Plant address: e.g. Andrews county, Texas, USA
5. Site latitude (decimal degrees): -33.456
6. Site longitude (decimal degrees): 45.384
7. Confirm whether the Plant was commissioned in multiple phases or in a single phase. Respond with 'Yes' or 'No'
8. Please provide: i) The overall Commercial Operation Date (format: YYYY/MM/DD) ii) If the plant was commissioned in multiple phases, list each phase by name (e.g., Phase 1, Phase2, etc.) alongside its individual Commercial Operation Date (format: YYYY/MM/DD).

PPA details:

Please provide the PPA details for the {plant_name} in {country}, using the exact format below and filling in each field with the correct information:
1. Number of PPA plant have and following details for each
i. Contracted capacity: e.g., 500 MW
ii. Start date (YYYY/MM/DD): e.g., 2011/09/08
iii. End date (YYYY/MM/DD): e.g., 2029/12/01
iv. Tenure: e.g., 25 years
v. Respondent name: e.g., Eskom
vi. Capacity contracted with each respondent: e.g., 500 MW
vii. Tariff price: e.g., 10
viii. Tariff currency: e.g., AUD
ix. Tariff unit: e.g., AUD/kWh

Power generation:

Please provide for the {plant_name} in {country}:
1. CUF of the hybrid plant (e.g 0.28, for each reporting year: 2019–20, 2020–21, 2021–22, 2022–23, 2023–24, find at least last three years)
2. Annual net generation in MWh for each reporting year: 2019–20, 2020–21, 2021–22, 2022–23, 2023–24 (Note: if the annual generation is not available use CUF to extract annual power generation)
3. AUX percentage (self consumption by plant) e.g. 0.5% [Note: if not available take as 0.5%]

Grid connected details:

Please provide the grid connectivity details for the {plant_name} in {country}, using the exact format below and filling in each field with the correct information:
1. Name of the substation to which the power station is connected: e.g., Bluewaters Substation
2. Latitude of the substation (decimal degrees): e.g., -35.014
3. Longitude of the substation (decimal degrees): e.g., 158.030
4. Line capacity of the substation: e.g., 400 kV
5. Linear distance of the substation from the power station: e.g., 26 km
"""


def detect_plant_technology(plant_name: str, discovered_data: dict = None) -> str:
    """
    Detect the plant technology type based on plant name and discovered data
    
    Args:
        plant_name: Name of the plant
        discovered_data: Any previously discovered data about the plant
        
    Returns:
        Technology type: 'solar', 'wind', 'hybrid', or 'unknown'
    """
    plant_name_lower = plant_name.lower()
    
    # Check plant name for technology indicators
    if any(keyword in plant_name_lower for keyword in ['solar', 'pv', 'photovoltaic']):
        return 'solar'
    elif any(keyword in plant_name_lower for keyword in ['wind', 'windfarm', 'wind farm']):
        return 'wind'
    elif any(keyword in plant_name_lower for keyword in ['hybrid', 'battery', 'storage']):
        return 'hybrid'
    
    # Check discovered data if available
    if discovered_data:
        tech_info = str(discovered_data).lower()
        if any(keyword in tech_info for keyword in ['solar', 'pv', 'photovoltaic']):
            if any(keyword in tech_info for keyword in ['battery', 'storage']):
                return 'hybrid'
            return 'solar'
        elif any(keyword in tech_info for keyword in ['wind']):
            return 'wind'
    
    # Default to solar if unable to determine
    return 'solar'


def get_technology_specific_prompt(plant_name: str, country: str, technology: str = None) -> str:
    """
    Get the appropriate prompt based on plant technology
    
    Args:
        plant_name: Name of the plant
        country: Country where the plant is located
        technology: Specific technology type (optional, will be auto-detected if not provided)
        
    Returns:
        Technology-specific prompt string
    """
    if not technology:
        technology = detect_plant_technology(plant_name)
    
    if technology == 'solar':
        return get_solar_plant_prompt(plant_name, country)
    elif technology == 'wind':
        return get_wind_plant_prompt(plant_name, country)
    elif technology == 'hybrid':
        return get_hybrid_plant_prompt(plant_name, country)
    else:
        # Default to solar prompt
        return get_solar_plant_prompt(plant_name, country)
