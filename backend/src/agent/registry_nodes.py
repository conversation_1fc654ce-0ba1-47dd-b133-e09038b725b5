"""
Registry Nodes for LangGraph Integration

This module contains LangGraph nodes that handle plant registry operations,
UID generation, and entity extraction triggering within the workflow.
"""

import asyncio
import threading
from typing import Dict, Any, List
from agent.state import OverallState
from agent.database_manager import get_database_manager, DiscoveryStatus
from agent.utils import get_research_topic
from google.genai import Client
import os


def get_web_search_function():
    """
    Get the web search function using Google Search API implementation
    """
    genai_client = Client(api_key=os.getenv("GEMINI_API_KEY"))

    def perform_web_search(query: str):
        """
        Perform web search using Google Search API via Gemini

        Args:
            query: Search query string

        Returns:
            List of search results
        """
        try:
            # Use Google Search API through genai client
            from agent.prompts import web_searcher_instructions, get_current_date
            from agent.configuration import Configuration
            from langchain_core.runnables import RunnableConfig

            configurable = Configuration.from_runnable_config(RunnableConfig(configurable={}))
            current_date = get_current_date()

            # Format the search prompt
            formatted_prompt = web_searcher_instructions.format(
                current_date=current_date,
                research_topic=query
            )

            # Use Google Search API through genai client
            response = genai_client.models.generate_content(
                model=configurable.web_searcher_model,
                contents=formatted_prompt,
                config={
                    "tools": [{"google_search": {}}],
                    "temperature": 0,
                    "max_output_tokens": 2048,
                    "top_k": 40,
                    "top_p": 0.95,
                }
            )

            # Extract search results from response
            if response and hasattr(response, 'text'):
                results = []
                content = response.text

                # Create a comprehensive result from the search
                results.append({
                    "title": f"Search results for: {query}",
                    "content": content,
                    "url": "https://search.google.com"
                })

                return results
            else:
                return []

        except Exception as e:
            print(f"⚠️ Web search failed for query '{query}': {e}")
            return []

    return perform_web_search


def check_plant_registry(state: OverallState) -> Dict[str, Any]:
    """
    LangGraph node to check if plant exists in registry
    
    This node checks if the input plant exists in the database and
    determines the next routing step.
    
    Args:
        state: Current graph state
        
    Returns:
        Updated state with plant registry information
    """
    session_id = state.get("session_id", "unknown")
    messages = state.get("messages", [])
    
    # Extract plant name from messages
    plant_name = ""
    if messages:
        plant_name = get_research_topic(messages)
    
    if not plant_name:
        print(f"[Session {session_id}] ❌ No plant name found in messages")
        return {
            "plant_exists_in_db": False,
            "plant_registry_error": "No plant name found in messages"
        }
    
    print(f"[Session {session_id}] 🔍 Checking plant registry for: {plant_name}")
    
    # Check database
    db_manager = get_database_manager()
    plant_info = db_manager.check_plant_exists(plant_name)
    
    if plant_info:
        print(f"[Session {session_id}] ✅ Plant found in registry:")
        print(f"   Organization: {plant_info['org_name']}")
        print(f"   Country: {plant_info['country']}")
        print(f"   Org UID: {plant_info['org_uid']}")
        print(f"   Plant UID: {plant_info.get('plant_uid', 'Not set')}")
        print(f"   Status: {plant_info['plant_status']}")
        
        return {
            "plant_exists_in_db": True,
            "plant_registry_info": plant_info,
            "org_uid": plant_info["org_uid"],
            "plant_uid": plant_info.get("plant_uid"),
            "org_name": plant_info["org_name"],
            "country": plant_info["country"]
        }
    else:
        print(f"[Session {session_id}] ❌ Plant not found in registry: {plant_name}")
        return {
            "plant_exists_in_db": False,
            "plant_registry_info": None
        }


def quick_org_discovery_node(state: OverallState) -> Dict[str, Any]:
    """
    LangGraph node for quick organization discovery

    This node performs actual web search-based organization discovery to find:
    1. The real organization that owns the plant
    2. All plants owned by that organization
    3. Country and operational details

    Args:
        state: Current graph state

    Returns:
        Updated state with discovered organization plants
    """
    session_id = state.get("session_id", "unknown")
    messages = state.get("messages", [])

    # Extract plant name from messages
    plant_name = ""
    if messages:
        plant_name = get_research_topic(messages)

    print(f"[Session {session_id}] 🔍 Starting quick organization discovery for: {plant_name}")

    try:
        # Import and use the proper quick discovery function
        from agent.quick_org_discovery import perform_quick_org_discovery

        # Get web search function
        web_search_fn = get_web_search_function()

        # Perform actual organization discovery with web search
        print(f"[Session {session_id}] 🌐 Running web search-based organization discovery...")
        org_info = perform_quick_org_discovery(plant_name, web_search_fn)

        print(f"[Session {session_id}] ✅ Quick discovery complete")
        print(f"[Session {session_id}]    Organization: {org_info['org_name']}")
        print(f"[Session {session_id}]    Country: {org_info['country']}")
        print(f"[Session {session_id}]    Plants discovered: {len(org_info['plants'])}")

        # CRITICAL: Always check for AGI context first - NEVER generate UUID if AGI entity_id exists
        entity_id = state.get("entity_id")
        if entity_id:
            print(f"[Session {session_id}] 🔧 AGI context detected - MUST use AGI entity_id as org_uid")
            print(f"[Session {session_id}]    AGI Entity ID: {entity_id}")
            print(f"[Session {session_id}] 🚨 NEVER generating new UUID - using AGI entity_id")
            org_uid_to_use = entity_id
        else:
            # Check database for existing org_uid (non-AGI flow)
            print(f"[Session {session_id}] 🔧 Normal flow - no AGI entity_id provided")
            print(f"[Session {session_id}] 🔍 Checking database for existing org_uid")
            from agent.database_manager import get_database_manager
            db_manager = get_database_manager()
            org_uid_to_use = db_manager.get_org_uid_from_database(org_info["org_name"], org_info["country"])

            if org_uid_to_use:
                print(f"[Session {session_id}] ✅ Found existing org_uid in database: {org_uid_to_use}")
            else:
                print(f"[Session {session_id}] ❌ No org_uid found in database")
                print(f"[Session {session_id}] 🔧 Generating temporary org_uid for normal flow processing")
                # Generate temporary org_uid for processing
                import uuid
                org_uid_to_use = f"temp_org_{str(uuid.uuid4())[:8]}"
                print(f"[Session {session_id}] ⚠️ Using temporary org_uid: {org_uid_to_use}")

        # Return comprehensive discovery results - PRESERVE ALL STATE INCLUDING entity_id
        return {
            **state,  # Preserve all existing state including entity_id
            "org_discovery_complete": True,
            "discovered_org_name": org_info["org_name"],
            "discovered_country": org_info["country"],
            "discovered_plants": org_info["plants"],
            "org_uid": org_uid_to_use,  # Use AGI entity_id or generated UID
            "discovery_session_id": session_id,
            "discovered_org_info": org_info  # Full org info for later use
        }

    except Exception as e:
        error_msg = f"Quick discovery failed: {str(e)}"
        print(f"[Session {session_id}] ❌ {error_msg}")

        # Fallback to basic extraction if web search fails
        print(f"[Session {session_id}] 🔄 Falling back to basic organization extraction...")

        # Extract organization name from plant name as fallback
        org_name = plant_name
        suffixes_to_remove = [
            " Power Plant", " Power Station", " Plant", " Station",
            " Solar Plant", " Solar Farm", " Solar Park", " Solar",
            " Wind Farm", " Wind Plant", " Thermal Plant"
        ]

        for suffix in suffixes_to_remove:
            if plant_name.endswith(suffix):
                org_name = plant_name[:-len(suffix)].strip()
                break

        # Create basic fallback data
        discovered_plants = [{"name": plant_name, "status": "operational", "country": "Unknown"}]

        # CRITICAL: Always check for AGI context in fallback case too - NEVER generate UUID if AGI entity_id exists
        entity_id = state.get("entity_id")
        if entity_id:
            print(f"[Session {session_id}] 🔧 AGI context detected in fallback - MUST use AGI entity_id as org_uid")
            print(f"[Session {session_id}]    AGI Entity ID: {entity_id}")
            print(f"[Session {session_id}] 🚨 NEVER generating new UUID in fallback - using AGI entity_id")
            org_uid_to_use = entity_id
        else:
            # Check database for existing org_uid (non-AGI fallback flow)
            print(f"[Session {session_id}] 🔧 Normal flow fallback - no AGI entity_id provided")
            print(f"[Session {session_id}] 🔍 Checking database for existing org_uid")
            from agent.database_manager import get_database_manager
            db_manager = get_database_manager()
            org_uid_to_use = db_manager.get_org_uid_from_database(org_name, "Unknown")

            if org_uid_to_use:
                print(f"[Session {session_id}] ✅ Found existing org_uid in database: {org_uid_to_use}")
            else:
                print(f"[Session {session_id}] ❌ No org_uid found in database for fallback")
                print(f"[Session {session_id}] 🔧 Generating temporary org_uid for fallback processing")
                # Generate temporary org_uid for processing
                import uuid
                org_uid_to_use = f"temp_org_{str(uuid.uuid4())[:8]}"
                print(f"[Session {session_id}] ⚠️ Using temporary org_uid: {org_uid_to_use}")

        return {
            "org_discovery_complete": True,
            "discovered_org_name": org_name,
            "discovered_country": "Unknown",
            "discovered_plants": discovered_plants,
            "org_uid": org_uid_to_use,  # Use AGI entity_id or generated UID
            "discovery_session_id": session_id,
            "discovery_error": error_msg
        }


def generate_uid_node(state: OverallState) -> Dict[str, Any]:
    """
    LangGraph node to generate organization and plant UIDs
    
    This node generates unique identifiers for organizations and plants
    and stores them in the database.
    
    Args:
        state: Current graph state
        
    Returns:
        Updated state with generated UIDs
    """
    session_id = state.get("session_id", "unknown")

    # Debug: Print entire state to see what we received
    print(f"[Session {session_id}] 🔍 DEBUG: generate_uid_node received state keys: {list(state.keys())}")
    print(f"[Session {session_id}] 🔍 DEBUG: org_discovery_complete = {state.get('org_discovery_complete')}")
    print(f"[Session {session_id}] 🔍 DEBUG: discovered_org_name = '{state.get('discovered_org_name')}'")
    print(f"[Session {session_id}] 🔍 DEBUG: discovered_country = '{state.get('discovered_country')}'")

    # Get organization info from state
    org_name = state.get("discovered_org_name", "")
    country = state.get("discovered_country", "Unknown")

    if not org_name:
        print(f"[Session {session_id}] ❌ No organization name found for UID generation")
        print(f"[Session {session_id}] ❌ DEBUG: org_name value: '{org_name}' (type: {type(org_name)})")
        return {
            "uid_generation_error": "No organization name found"
        }
    
    print(f"[Session {session_id}] 🔑 Generating UIDs for organization: {org_name}")

    # CRITICAL: Always check database first for existing AGI entity_id
    db_manager = get_database_manager()
    entity_id = state.get("entity_id")
    existing_org_uid = state.get("org_uid")

    # Step 1: Check if AGI entity_id is provided and should be used
    if entity_id:
        print(f"[Session {session_id}] 🔧 AGI context detected - MUST use AGI entity_id")
        print(f"[Session {session_id}]    AGI Entity ID: {entity_id}")
        print(f"[Session {session_id}] 🚨 NEVER generate new UUID when AGI entity_id exists")
        org_uid = entity_id
    # Step 2: Check if org_uid already exists in state
    elif existing_org_uid:
        print(f"[Session {session_id}] 🔧 Using existing org_uid from state: {existing_org_uid}")
        org_uid = existing_org_uid
    # Step 3: Check database for any existing org_uid for this organization
    else:
        print(f"[Session {session_id}] 🔍 Checking database for existing org_uid for: {org_name}")
        # Try to find existing organization in database
        try:
            session_db = db_manager.get_session()
            from agent.database_manager import PowerPlantRegistry
            existing_org = session_db.query(PowerPlantRegistry).filter_by(org_name=org_name).first()
            session_db.close()

            if existing_org and existing_org.org_uid:
                print(f"[Session {session_id}] ✅ Found existing org_uid in database: {existing_org.org_uid}")
                org_uid = existing_org.org_uid
            else:
                # For normal flow, generate temporary org_uid for processing
                print(f"[Session {session_id}] ❌ No existing org_uid found in database")
                print(f"[Session {session_id}] 🔧 Generating temporary org_uid for processing")
                import uuid
                org_uid = f"temp_org_{str(uuid.uuid4())[:8]}"
                print(f"[Session {session_id}] ⚠️ Using temporary org_uid: {org_uid}")
        except Exception as e:
            print(f"[Session {session_id}] ❌ Error checking database: {e}")
            print(f"[Session {session_id}] 🔧 Generating temporary org_uid due to database error")
            import uuid
            org_uid = f"temp_org_{str(uuid.uuid4())[:8]}"
            print(f"[Session {session_id}] ⚠️ Using temporary org_uid: {org_uid}")

    return {
        **state,  # Preserve all existing state including entity_id
        "org_uid": org_uid,
        "uid_generation_complete": True,
        "generated_org_uid": org_uid
    }


def populate_database_async_node(state: OverallState) -> Dict[str, Any]:
    """
    LangGraph node to populate database with discovered plants
    
    This node saves the discovered organization and plants to the database
    with generated UIDs.
    
    Args:
        state: Current graph state
        
    Returns:
        Updated state with database population results
    """
    session_id = state.get("session_id", "unknown")
    
    # Get required data from state
    org_name = state.get("discovered_org_name", "")
    country = state.get("discovered_country", "Unknown")
    org_uid = state.get("org_uid", "")
    discovered_plants = state.get("discovered_plants", [])
    
    if not all([org_name, org_uid, discovered_plants]):
        print(f"[Session {session_id}] ❌ Missing required data for database population")
        return {
            "database_population_error": "Missing required data"
        }
    
    print(f"[Session {session_id}] 💾 Populating database with {len(discovered_plants)} plants")
    
    # Get input plant name for tracking
    messages = state.get("messages", [])
    input_plant_name = ""
    if messages:
        input_plant_name = get_research_topic(messages)
    
    # Save to database
    db_manager = get_database_manager()
    success = db_manager.save_organization_plants(
        org_name=org_name,
        country=country,
        plants_data=discovered_plants,
        org_uid=org_uid,
        discovery_session_id=session_id,
        discovered_from_plant=input_plant_name
    )
    
    if success:
        print(f"[Session {session_id}] ✅ Database population completed")

        # CRITICAL: Fetch plant_uid from database and update state
        # This is essential for AGI flows where plant_uid is generated during database save
        try:
            plant_info = db_manager.check_plant_exists(input_plant_name)
            if plant_info and plant_info.get("plant_uid"):
                plant_uid = plant_info["plant_uid"]
                print(f"[Session {session_id}] 🔑 Retrieved plant_uid from database: {plant_uid}")
                return {
                    "database_population_complete": True,
                    "database_population_success": True,
                    "plant_uid": plant_uid  # Update state with plant_uid from database
                }
            else:
                print(f"[Session {session_id}] ⚠️ Could not retrieve plant_uid from database")
                return {
                    "database_population_complete": True,
                    "database_population_success": True
                }
        except Exception as e:
            print(f"[Session {session_id}] ⚠️ Error retrieving plant_uid: {e}")
            return {
                "database_population_complete": True,
                "database_population_success": True
            }
    else:
        print(f"[Session {session_id}] ❌ Database population failed")
        return {
            "database_population_complete": True,
            "database_population_success": False,
            "database_population_error": "Failed to save to database"
        }


def entity_extraction_trigger_node(state: OverallState) -> Dict[str, Any]:
    """
    LangGraph node to trigger entity-level extraction

    This node starts the entity-level extraction process for all plants
    in the organization after database population is complete.

    Args:
        state: Current graph state

    Returns:
        Updated state with entity extraction trigger results
    """
    session_id = state.get("session_id", "unknown")
    discovered_plants = state.get("discovered_plants", [])

    print(f"[Session {session_id}] 🚀 Entity extraction trigger activated")
    print(f"[Session {session_id}] Plants to process: {len(discovered_plants)}")

    if len(discovered_plants) <= 1:
        print(f"[Session {session_id}] ⚠️ Only {len(discovered_plants)} plant(s) found - skipping entity extraction")
        return {
            "entity_extraction_triggered": False,
            "entity_extraction_reason": "Insufficient plants for entity extraction"
        }

    try:
        # Import and initialize entity extraction controller
        from agent.entity_extraction_controller import EntityExtractionController

        # Get the input plant name that triggered this discovery
        messages = state.get("messages", [])
        input_plant_name = ""
        if messages:
            input_plant_name = get_research_topic(messages)

        if not input_plant_name:
            print(f"[Session {session_id}] ❌ Could not determine input plant name")
            return {
                "entity_extraction_triggered": False,
                "entity_extraction_error": "Could not determine input plant name"
            }

        print(f"[Session {session_id}] 🎯 SIMPLE FOR-LOOP PROCESSING")
        print(f"[Session {session_id}] 📋 Will process {len(discovered_plants)} plants")

        # SIMPLE FOR-LOOP: Process each plant
        org_uid = state.get("org_uid", "")
        organization_name = state.get("discovered_org_name", "Unknown Organization")

        # STEP 1: Process organization data ONCE with WEB RESEARCH (before plant loop)
        print(f"[Session {session_id}] 🏢 Processing organization data with web research...")

        # Create organization state for web research
        from langchain_core.messages import HumanMessage
        org_state = {
            "messages": [HumanMessage(content=organization_name)],
            "session_id": f"{session_id}_org",
            "research_topic": organization_name,
            "search_phase": 1,  # Organization-level research
            "research_loop_count": 0,
            "web_research_result": [],
            "search_query": [],
            "sources_gathered": [],
            "continue_research": False,
            "phase_complete": False,
            "initial_search_query_count": 5,
            "max_research_loops": 3,
            "reasoning_model": state.get("reasoning_model", ""),
            "org_uid": org_uid,
            "entity_id": state.get("entity_id"),
            # CRITICAL: Ensure organization name is available for JSON generation
            "discovered_org_name": organization_name,
            "organization_name": organization_name
        }

        # Run organization web research
        from agent.graph import generate_query, web_research, reflection, finalize_answer_parallel

        # Generate queries for organization
        query_result = generate_query(org_state, {})
        org_state.update(query_result)

        # Run 3 research loops for organization
        for loop in range(3):
            print(f"[Session {session_id}] 🔍 Organization research loop {loop+1}/3")

            # Web research
            research_result = web_research(org_state, {})
            org_state.update(research_result)

            # Reflection
            reflection_result = reflection(org_state, {})
            org_state.update(reflection_result)

            if not org_state.get("continue_research", False):
                break

        # Generate organization JSON using research data
        print(f"[Session {session_id}] 🏢 Generating organization JSON with research data...")
        finalize_result = finalize_answer_parallel(org_state, {})
        org_state.update(finalize_result)

        print(f"[Session {session_id}] ✅ Organization web research and JSON generation completed")

        print(f"[Session {session_id}] 🔄 FOR-LOOP START: Processing {len(discovered_plants)} plants")

        # Initialize results tracking
        all_results = []

        for i, plant in enumerate(discovered_plants):
            plant_name = plant.get("name", f"Plant_{i+1}")
            print(f"[Session {session_id}] 🌱 FOR-LOOP: Plant {i+1}/{len(discovered_plants)}: {plant_name}")

            # CRITICAL: Ensure this plant has a UID in database
            from agent.database_manager import get_database_manager
            db_manager = get_database_manager()
            existing_plant = db_manager.check_plant_exists(plant_name)

            if existing_plant:
                plant_uid = existing_plant.get("plant_uid")
                print(f"[Session {session_id}] ✅ Plant UID found in DB: {plant_uid}")
            else:
                print(f"[Session {session_id}] ❌ WARNING: Plant not found in database: {plant_name}")
                print(f"[Session {session_id}] 🔧 This should not happen - all plants should be in DB after discovery")

            try:
                # STEP 1: Image extraction FIRST (so URLs can be included in plant JSON)
                print(f"[Session {session_id}] 🖼️ Extracting images for: {plant_name}")
                from agent.image_extraction import extract_and_upload_images
                s3_image_urls = extract_and_upload_images(plant_name, f"{session_id}_p{i+1}")
                print(f"[Session {session_id}] ✅ Images: {len(s3_image_urls) if s3_image_urls else 0}")

                # STEP 2: Web research for this plant
                print(f"[Session {session_id}] 🔍 Starting web research for: {plant_name}")
                from langchain_core.messages import HumanMessage

                # Create individual plant state for web research
                plant_state = {
                    "messages": [HumanMessage(content=plant_name)],
                    "session_id": f"{session_id}_p{i+1}",
                    "research_topic": plant_name,
                    "search_phase": 2,  # Plant-level research
                    "research_loop_count": 0,
                    "web_research_result": [],
                    "search_query": [],
                    "sources_gathered": [],
                    "continue_research": False,
                    "phase_complete": False,
                    "initial_search_query_count": 5,
                    "max_research_loops": 3,
                    "reasoning_model": state.get("reasoning_model", ""),
                    "org_uid": org_uid,
                    "entity_id": state.get("entity_id"),
                    "s3_image_urls": s3_image_urls  # CRITICAL: Pass image URLs to plant JSON generation
                }

                # Run web research pipeline for this plant
                from agent.graph import generate_query, web_research, reflection, finalize_answer_parallel

                # Generate queries
                query_result = generate_query(plant_state, {})
                plant_state.update(query_result)

                # Run 3 research loops
                for loop in range(3):
                    print(f"[Session {session_id}] 🔍 Research loop {loop+1}/3 for: {plant_name}")

                    # Web research
                    research_result = web_research(plant_state, {})
                    plant_state.update(research_result)

                    # Reflection
                    reflection_result = reflection(plant_state, {})
                    plant_state.update(reflection_result)

                    if not plant_state.get("continue_research", False):
                        break

                # STEP 3: Generate plant JSON using research data AND image URLs
                print(f"[Session {session_id}] 🏭 Generating plant JSON with research data AND image URLs for: {plant_name}")
                finalize_result = finalize_answer_parallel(plant_state, {})
                plant_state.update(finalize_result)

                print(f"[Session {session_id}] ✅ Plant processing completed with images included: {plant_name}")

                print(f"[Session {session_id}] ✅ Completed full pipeline for: {plant_name}")

                # Add successful result
                all_results.append({
                    "plant_name": plant_name,
                    "status": "completed",
                    "plant_index": i + 1
                })

            except Exception as e:
                print(f"[Session {session_id}] ❌ Error processing {plant_name}: {str(e)}")
                import traceback
                print(f"[Session {session_id}] ❌ Error details: {traceback.format_exc()}")

                # Add failed result
                all_results.append({
                    "plant_name": plant_name,
                    "status": "failed",
                    "error": str(e),
                    "plant_index": i + 1
                })

        print(f"[Session {session_id}] 🎉 FOR-LOOP COMPLETE: Processed {len(discovered_plants)} plants")

        print(f"[Session {session_id}] 🎉 Entity extraction completed!")
        print(f"[Session {session_id}] ✅ Processed {len(all_results)} plants")

        # Save final results to DynamoDB
        try:
            print(f"[Session {session_id}] 💾 Saving details to DynamoDB...")

            # Get organization details from state
            organization_name = state.get("discovered_org_name", "Unknown Organization")
            plant_names = [plant.get("name", "Unknown Plant") for plant in discovered_plants]

            print(f"[Session {session_id}] 🔍 Organization data for DynamoDB:")
            print(f"   Organization: {organization_name}")
            print(f"   Org UID: {org_uid}")
            print(f"   Plants: {len(plant_names)} plants")
            print(f"   Plant Names: {', '.join(plant_names)}")

            # Save to DynamoDB
            from agent.dynamodb_manager import get_dynamodb_manager
            dynamodb_manager = get_dynamodb_manager()

            success = dynamodb_manager.save_organization_details(
                organization_name=organization_name,
                org_uid=org_uid,
                plant_names=plant_names
            )

            if success:
                print(f"[Session {session_id}] ✅ Organization details saved to DynamoDB")
            else:
                print(f"[Session {session_id}] ❌ Failed to save organization details to DynamoDB")

        except Exception as e:
            print(f"[Session {session_id}] ❌ DynamoDB save error: {str(e)}")

        return {
            **state,
            "entity_extraction_triggered": True,
            "entity_extraction_complete": True,
            "entity_extraction_results": all_results,
            "processed_plants_count": len(all_results),
            "organization_name": organization_name,
            "final_plant_names": plant_names
        }

    except Exception as e:
        error_msg = f"Entity extraction trigger failed: {str(e)}"
        print(f"[Session {session_id}] ❌ {error_msg}")
        return {
            "entity_extraction_triggered": False,
            "entity_extraction_error": error_msg
        }


# ===== ROUTING FUNCTIONS =====

def route_after_registry_check(state: OverallState) -> str:
    """
    Routing function after plant registry check
    
    Args:
        state: Current graph state
        
    Returns:
        Next node name
    """
    session_id = state.get("session_id", "unknown")
    
    if state.get("plant_exists_in_db", False):
        return "generate_uid"  # Skip discovery, go straight to UID
    else:
        return "quick_org_discovery"  # Need to discover organization


def route_after_uid_generation(state: OverallState) -> str:
    """
    Routing function after UID generation
    
    Args:
        state: Current graph state
        
    Returns:
        Next node name
    """
    return "populate_database_async"  # Always populate database after UID generation


def route_after_database_population(state: OverallState) -> str:
    """
    Routing function after database population
    
    Decides whether to trigger entity extraction or continue with single plant processing
    
    Args:
        state: Current graph state
        
    Returns:
        Next node name
    """
    session_id = state.get("session_id", "unknown")
    discovered_plants = state.get("discovered_plants", [])

    print(f"[Session {session_id}] 🔍 Discovered plants: {len(discovered_plants)}")

    # Decision: Entity extraction vs Single plant extraction
    if len(discovered_plants) > 1:
        print(f"[Session {session_id}] 🏭 Multiple plants detected ({len(discovered_plants)} plants)")
        print(f"[Session {session_id}] ➡️  ROUTING TO: entity_extraction_trigger (start entity-level extraction)")
        return "entity_extraction_trigger"
    else:
        print(f"[Session {session_id}] 🏭 Single plant detected")
        print(f"[Session {session_id}] ➡️  ROUTING TO: spawn_parallel_processing_with_uid (start 3-level + image extraction)")
        return "spawn_parallel_processing_with_uid"
