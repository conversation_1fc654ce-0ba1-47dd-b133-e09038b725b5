"""
Reference Data Module - Engineering calculations and industry standards
Based on: current state (PLF, PAF, Capacity factor).csv
"""

import math
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass

@dataclass
class CoalProperties:
    """Coal properties for emission calculations"""
    astm_gcv_range: Tuple[float, float]  # kcal/kg
    iea_gcv_range: Tuple[float, float]   # kcal/kg
    standard_gcv: float                  # kcal/kg
    emission_factor: float               # kg CO2/kg coal

@dataclass
class TechnologyStandards:
    """Technology-specific efficiency and operational standards"""
    pressure_range: str
    temperature_range: str
    standard_efficiency: float  # %
    aux_power_consumption: Dict[str, float]  # % by capacity range

class PowerPlantReferenceData:
    """Complete reference data for power plant calculations"""
    
    def __init__(self):
        self.setup_reference_data()
    
    def setup_reference_data(self):
        """Initialize all reference data from CSV"""
        
        # Coal Properties
        self.coal_properties = {
            "bituminous": CoalProperties(
                astm_gcv_range=(6394, 7784),
                iea_gcv_range=(5497, 7887),
                standard_gcv=6690,
                emission_factor=2.42
            ),
            "sub_bituminous": CoalProperties(
                astm_gcv_range=(4615, 6394),
                iea_gcv_range=(4159, 5712),
                standard_gcv=4900,
                emission_factor=1.67
            )
        }
        
        # Technology Standards
        self.technology_standards = {
            "subcritical": TechnologyStandards(
                pressure_range="<22.1 MPa",
                temperature_range="Up to 550°C",
                standard_efficiency=36.0,
                aux_power_consumption={
                    "<=250": 11.0,
                    "250-500": 10.0,
                    "500-750": 9.0,
                    "750-1000": 8.0,
                    ">1000": 7.0
                }
            ),
            "supercritical": TechnologyStandards(
                pressure_range="22.1-25 MPa",
                temperature_range="540-580°C",
                standard_efficiency=39.5,
                aux_power_consumption={
                    "<=250": 9.0,
                    "250-500": 8.0,
                    "500-750": 7.0,
                    "750-1000": 6.0,
                    ">1000": 5.0
                }
            ),
            "ultra_supercritical": TechnologyStandards(
                pressure_range=">25 MPa",
                temperature_range=">580°C",
                standard_efficiency=45.75,
                aux_power_consumption={
                    "<=250": 6.0,
                    "250-500": 5.5,
                    "500-750": 5.0,
                    "750-1000": 4.75,
                    ">1000": 4.5
                }
            ),
            "advanced_usc": TechnologyStandards(
                pressure_range=">30 MPa",
                temperature_range=">700°C",
                standard_efficiency=48.0,
                aux_power_consumption={
                    "<=250": 5.5,
                    "250-500": 5.25,
                    "500-750": 5.0,
                    "750-1000": 4.5,
                    ">1000": 4.0
                }
            )
        }
        
        # Additional Energy Source Properties (Standard Values)
        self.energy_properties = {
            "natural_gas": {
                "gcv": 35.3,  # MJ/m³ (standard value)
                "gcv_unit": "MJ/m³",
                "emission_factor": 2.35  # kg CO2/m³
            },
            "biomass": {
                "wood_chips": {
                    "gcv": 4200,  # kcal/kg (standard value)
                    "gcv_unit": "kcal/kg"
                },
                "agricultural_residue": {
                    "gcv": 3800,  # kcal/kg (standard value)
                    "gcv_unit": "kcal/kg"
                }
            }
        }
        
        # Gas Technology Standards (Standard Industry Values)
        self.gas_technology_standards = {
            "open_cycle_gas_turbine": {
                "efficiency": 35.0,  # % (standard OCGT efficiency)
                "heat_rate": 10300   # kJ/kWh (standard OCGT heat rate)
            },
            "closed_cycle_gas_turbine": {
                "efficiency": 45.0,  # % (standard CCGT efficiency)
                "heat_rate": 8000    # kJ/kWh (standard CCGT heat rate)
            },
            "combined_cycle": {
                "efficiency": 58.0,  # % (modern CCGT efficiency)
                "heat_rate": 6200    # kJ/kWh (modern CCGT heat rate)
            }
        }
        
        # Cofiring Standards
        self.cofiring_standards = {
            "efficiency_loss": 2.5  # % efficiency loss for biomass cofiring (standard value)
        }
        
        # Constants for calculations
        self.CONVERSION_CONSTANTS = {
            "kwh_to_kcal": 860.42,  # 1 kWh = 860.42 kcal
            "hours_per_year": 8760   # Annual hours
        }

# Global instance
REFERENCE_DATA = PowerPlantReferenceData()


class PowerPlantCalculator:
    """Engineering calculations for missing power plant data"""
    
    def __init__(self):
        self.ref_data = REFERENCE_DATA
    
    def _extract_numeric_value(self, value: Any) -> float:
        """Extract numeric value from various formats"""
        if isinstance(value, (int, float)):
            return float(value)
        
        if isinstance(value, str):
            # Remove common non-numeric characters
            cleaned = value.replace(",", "").replace("MW", "").replace("GW", "").replace("%", "").strip()
            try:
                return float(cleaned)
            except:
                return 0.0
        
        return 0.0
    
    def calculate_plf(self, annual_generation_mwh: float, capacity_mw: float) -> float:
        """
        Calculate Plant Load Factor
        PLF = (Annual generation) / (Maximum generation)
        """
        max_generation_mwh = capacity_mw * self.ref_data.CONVERSION_CONSTANTS["hours_per_year"] / 1000
        plf = (annual_generation_mwh / max_generation_mwh) * 100
        return round(plf, 2)
    
    def calculate_paf(self, available_hours: float, total_hours: float = None) -> float:
        """
        Calculate Plant Availability Factor
        PAF = (Available hours) / (Total operational hours)
        """
        if total_hours is None:
            total_hours = self.ref_data.CONVERSION_CONSTANTS["hours_per_year"]
        
        paf = (available_hours / total_hours) * 100
        return round(paf, 2)
    
    def estimate_auxiliary_power_consumption(self, capacity_mw: float, technology: str) -> float:
        """
        Estimate auxiliary power consumption based on capacity and technology
        """
        technology = technology.lower().replace("-", "_").replace(" ", "_")
        
        if technology not in self.ref_data.technology_standards:
            technology = "subcritical"  # Default fallback
        
        aux_power_map = self.ref_data.technology_standards[technology].aux_power_consumption
        
        # Determine capacity range
        if capacity_mw <= 250:
            return aux_power_map["<=250"]
        elif capacity_mw <= 500:
            return aux_power_map["250-500"]
        elif capacity_mw <= 750:
            return aux_power_map["500-750"]
        elif capacity_mw <= 1000:
            return aux_power_map["750-1000"]
        else:
            return aux_power_map[">1000"]
    
    def estimate_plant_efficiency(self, technology: str) -> float:
        """
        Estimate plant efficiency based on technology type
        """
        technology = technology.lower().replace("-", "_").replace(" ", "_")
        
        if technology in self.ref_data.technology_standards:
            return self.ref_data.technology_standards[technology].standard_efficiency
        
        return 36.0  # Default subcritical efficiency
    
    def calculate_emission_factor_from_coal(self, 
                                          annual_generation_kwh: float,
                                          coal_type: str,
                                          plant_efficiency: float = None,
                                          technology: str = "subcritical") -> Dict[str, Any]:
        """
        Calculate emission factor using Method 2 from CSV
        Based on South Africa 2023 methodology
        """
        coal_type = coal_type.lower().replace("-", "_")
        
        if coal_type not in self.ref_data.coal_properties:
            coal_type = "bituminous"  # Default
        
        if plant_efficiency is None:
            plant_efficiency = self.estimate_plant_efficiency(technology)
        
        coal_props = self.ref_data.coal_properties[coal_type]
        
        # Step 1: Heat generation by 1 kg coal (kWh)
        heat_per_kg_coal = coal_props.standard_gcv / self.ref_data.CONVERSION_CONSTANTS["kwh_to_kcal"]
        
        # Step 2: Electricity generated by 1 kg coal (kWh)
        electricity_per_kg_coal = heat_per_kg_coal * (plant_efficiency / 100)
        
        # Step 3: Coal usage (kg)
        coal_usage_kg = annual_generation_kwh / electricity_per_kg_coal
        
        # Step 4: Total CO2 produced (kg)
        total_co2_kg = coal_usage_kg * coal_props.emission_factor
        
        # Step 5: Emission factor (kg CO2/kWh)
        emission_factor = total_co2_kg / annual_generation_kwh
        
        return {
            "emission_factor": round(emission_factor, 4),
            "calculation_details": {
                "coal_type": coal_type,
                "plant_efficiency": plant_efficiency,
                "gcv_used": coal_props.standard_gcv,
                "heat_per_kg_coal": round(heat_per_kg_coal, 4),
                "electricity_per_kg_coal": round(electricity_per_kg_coal, 4),
                "coal_usage_kg": round(coal_usage_kg, 2),
                "total_co2_kg": round(total_co2_kg, 2),
                "coal_emission_factor": coal_props.emission_factor
            }
        }
    
    def validate_extracted_values(self, extracted_data: Dict, unit_info: Dict) -> Dict[str, Any]:
        """
        Validate extracted values against industry standards
        """
        validation_results = {}
        technology = unit_info.get("technology", "subcritical").lower()
        capacity = unit_info.get("capacity", 0)
        
        # Validate efficiency
        if "unit_efficiency" in extracted_data:
            expected_efficiency = self.estimate_plant_efficiency(technology)
            actual_efficiency = float(extracted_data["unit_efficiency"])
            
            if abs(actual_efficiency - expected_efficiency) > 10:  # 10% tolerance
                validation_results["efficiency_warning"] = {
                    "actual": actual_efficiency,
                    "expected": expected_efficiency,
                    "message": f"Efficiency {actual_efficiency}% seems unusual for {technology} (expected ~{expected_efficiency}%)"
                }
        
        # Validate auxiliary power consumption
        if "auxiliary_power_consumed" in extracted_data:
            expected_aux = self.estimate_auxiliary_power_consumption(capacity, technology)
            actual_aux_list = extracted_data["auxiliary_power_consumed"]
            
            if actual_aux_list:
                actual_aux = float(actual_aux_list[0].get("value", expected_aux))
                if abs(actual_aux - expected_aux) > 3:  # 3% tolerance
                    validation_results["aux_power_warning"] = {
                        "actual": actual_aux,
                        "expected": expected_aux,
                        "message": f"Auxiliary power {actual_aux}% unusual for {capacity}MW {technology} plant (expected ~{expected_aux}%)"
                    }
        
        return validation_results
    
    def get_technology_info(self, technology: str) -> Dict[str, Any]:
        """
        Get complete technology information
        """
        technology = technology.lower().replace("-", "_").replace(" ", "_")
        
        if technology in self.ref_data.technology_standards:
            tech_data = self.ref_data.technology_standards[technology]
            return {
                "technology": technology,
                "pressure_range": tech_data.pressure_range,
                "temperature_range": tech_data.temperature_range,
                "standard_efficiency": tech_data.standard_efficiency,
                "aux_power_ranges": tech_data.aux_power_consumption
            }
        
        return None
    
    def get_coal_gcv(self, coal_type: str = "bituminous") -> float:
        """Get GCV value for coal type"""
        coal_type = coal_type.lower().replace("-", "_")
        if coal_type in self.ref_data.coal_properties:
            return self.ref_data.coal_properties[coal_type].standard_gcv
        return self.ref_data.coal_properties["bituminous"].standard_gcv  # Default
    
    def get_biomass_gcv(self, biomass_type: str = "wood_chips") -> float:
        """Get GCV value for biomass type"""
        biomass_type = biomass_type.lower().replace(" ", "_")
        if biomass_type in self.ref_data.energy_properties["biomass"]:
            return self.ref_data.energy_properties["biomass"][biomass_type]["gcv"]
        return self.ref_data.energy_properties["biomass"]["wood_chips"]["gcv"]  # Default
    
    def get_natural_gas_gcv(self) -> float:
        """Get GCV value for natural gas"""
        return self.ref_data.energy_properties["natural_gas"]["gcv"]
    
    def get_ocgt_efficiency(self) -> float:
        """Get Open Cycle Gas Turbine efficiency"""
        return self.ref_data.gas_technology_standards["open_cycle_gas_turbine"]["efficiency"]
    
    def get_ccgt_efficiency(self) -> float:
        """Get Combined Cycle Gas Turbine efficiency"""
        return self.ref_data.gas_technology_standards["combined_cycle"]["efficiency"]
    
    def get_ocgt_heat_rate(self) -> float:
        """Get Open Cycle Gas Turbine heat rate"""
        return self.ref_data.gas_technology_standards["open_cycle_gas_turbine"]["heat_rate"]
    
    def get_ccgt_heat_rate(self) -> float:
        """Get Combined Cycle Gas Turbine heat rate"""
        return self.ref_data.gas_technology_standards["combined_cycle"]["heat_rate"]
    
    def get_cofiring_efficiency_loss(self) -> float:
        """Get efficiency loss for biomass cofiring"""
        return self.ref_data.cofiring_standards["efficiency_loss"]

# Global calculator instance
CALCULATOR = PowerPlantCalculator()