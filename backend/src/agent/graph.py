import os
import re
import json
import uuid
import time
from typing import List

from agent.tools_and_schemas import SearchQueryList, Reflection, PlantLevelInfo, UnitLevelInfo
from agent.utils import get_research_topic
from agent.plant_prompts import get_technology_specific_prompt, detect_plant_technology
from agent.dynamodb_manager import get_dynamodb_manager
from dotenv import load_dotenv
from langchain_core.messages import AIMessage
from langgraph.types import Send
from langgraph.graph import StateGraph
from langgraph.graph import START, END
from langchain_core.runnables import RunnableConfig
from google.genai import Client

from agent.state import (
    OverallState,
    UnitState,
    QueryGenerationState,
    ReflectionState,
    WebSearchState,
    merge_unit_matrices,
    merge_unit_flags,
)
from agent.configuration import Configuration
from agent.prompts import (
    get_current_date,
    query_writer_instructions,
    web_searcher_instructions,
    reflection_instructions,
    answer_instructions,
    unit_query_writer_instructions,
    unit_web_searcher_instructions,
    unit_reflection_instructions,
    unit_answer_instructions,
)
from langchain_google_genai import ChatGoogleGenerativeA<PERSON>
from agent.utils import (
    get_citations,
    get_research_topic,
    insert_citation_markers,
    resolve_urls,
)
from agent.image_extraction import extract_and_upload_images
from agent.json_s3_storage import (
    store_organization_data,
    store_plant_data,
    get_plant_s3_urls,
    check_s3_connection,
    sanitize_plant_name
)
from agent.fallback_calculations import FallbackCalculationEngine
from agent.registry_nodes import (
    check_plant_registry,
    quick_org_discovery_node,
    generate_uid_node,
    populate_database_async_node,
    entity_extraction_trigger_node,
    route_after_registry_check,
    route_after_uid_generation,
    route_after_database_population
)

def save_agi_org_uid_directly(state: OverallState) -> OverallState:
    """
    Save AGI-provided org UID directly to database and state

    This function handles the AGI Layer integration where the organization UID
    is provided as entity_id and needs to be saved for use throughout the pipeline.

    CRITICAL: Always save and use the AGI entity_id, never generate new UUIDs.
    """
    session_id = state.get("session_id", "unknown")

    try:
        # Get AGI-provided entity_id (org UID)
        entity_id = state.get("entity_id")
        plant_name = get_research_topic(state.get("messages", []))

        if not entity_id:
            print(f"[Session {session_id}] ❌ No entity_id provided by AGI Layer")
            return {
                **state,
                "agi_uid_saved": False,
                "error": "No entity_id from AGI Layer"
            }

        print(f"[Session {session_id}] 🔧 AGI UID INTEGRATION - IMMEDIATE SAVE")
        print(f"[Session {session_id}] Plant: {plant_name}")
        print(f"[Session {session_id}] AGI Entity ID (Org UID): {entity_id}")
        print(f"[Session {session_id}] 🚨 CRITICAL: Using AGI entity_id, NOT generating new UUID")

        # STEP 1: TEMPORARILY SKIP database save to focus on sequential processing
        print(f"[Session {session_id}] 💾 STEP 1: Skipping immediate database save (temporary)")
        print(f"[Session {session_id}] 🔧 Will save AGI entity_id after discovery completes")

        # STEP 2: Skip plant existence check for now
        existing_plant = None

        # SIMPLIFIED: Just return with AGI entity_id as org_uid
        print(f"[Session {session_id}] 🔧 Using AGI Entity ID as org_uid: {entity_id}")
        print(f"[Session {session_id}] 🔧 Plant will be processed through discovery flow")

        # ALWAYS use AGI-provided entity_id as org_uid
        return {
            **state,
            "org_uid": entity_id,  # ALWAYS use AGI entity_id
            "plant_uid": None,  # Will be generated later
            "agi_uid_saved": True,
            "plant_exists_in_db": False,
            "error": None
        }

    except Exception as e:
        print(f"[Session {session_id}] ❌ Error saving AGI UID: {e}")
        return {
            **state,
            "agi_uid_saved": False,
            "error": str(e)
        }

def convert_org_numeric_fields(org_data: dict, session_id: str = "unknown") -> dict:
    """
    Convert string values to numeric values for organization-level data.
    
    This function processes the organization data to ensure numeric values are stored as 
    numbers (integers or decimals) rather than strings with quotes.
    """
    # Currently, there are no specific numeric fields in the organization data
    # that need conversion, but we can add them if needed in the future
    
    # Example of how to convert a field if needed:
    # if "some_numeric_field" in org_data and isinstance(org_data["some_numeric_field"], str):
    #     try:
    #         numeric_value = extract_numeric_value(org_data["some_numeric_field"])
    #         if numeric_value == int(numeric_value):
    #             org_data["some_numeric_field"] = int(numeric_value)
    #         else:
    #             org_data["some_numeric_field"] = numeric_value
    #     except Exception as e:
    #         print(f"[Session {session_id}] ⚠️ Could not convert some_numeric_field to numeric: {str(e)}")
    
    return org_data

def convert_numeric_fields(plant_data: dict, session_id: str = "unknown") -> dict:
    """
    Convert string values to numeric values for specific fields.
    
    This function processes the plant data to ensure numeric values are stored as 
    numbers (integers or decimals) rather than strings with quotes.
    """
    # Define fields that should be numeric
    simple_numeric_fields = [
        "annual_operational_hours", 
        "installed_bess_capacity", 
        "installed_biomass_capacity", 
        "installed_solar_capacity",
        "installed_wind_capacity",
        "latitude", 
        "longitude", 
        "plant_lifetime",
        "total_installed_capacity"
    ]
    
    # Define nested fields that should be numeric
    nested_numeric_fields = {
        "auxiliary_power_consumed": ["value"],
        "cuf": ["value"],
        "gross_power_generation": ["value"],
        "ppa_details": {
            "self": ["capacity"],
            "respondents": ["capacity", "price"]
        }
    }
    
    # Process simple numeric fields
    for field in simple_numeric_fields:
        if field in plant_data and isinstance(plant_data[field], str):
            try:
                # Try to convert to float first
                numeric_value = extract_numeric_value(plant_data[field])
                
                # If it's a whole number, convert to int
                if numeric_value == int(numeric_value):
                    plant_data[field] = int(numeric_value)
                else:
                    plant_data[field] = numeric_value
                    
                print(f"[Session {session_id}] 🔢 Converted {field} to numeric: {plant_data[field]}")
            except Exception as e:
                print(f"[Session {session_id}] ⚠️ Could not convert {field} to numeric: {str(e)}")
    
    # Process nested numeric fields
    for parent_field, child_fields in nested_numeric_fields.items():
        if parent_field in plant_data and isinstance(plant_data[parent_field], list):
            for item in plant_data[parent_field]:
                if isinstance(item, dict):
                    # Handle simple nested fields
                    if isinstance(child_fields, list):
                        for child_field in child_fields:
                            if child_field in item and isinstance(item[child_field], str):
                                try:
                                    numeric_value = extract_numeric_value(item[child_field])
                                    if numeric_value == int(numeric_value):
                                        item[child_field] = int(numeric_value)
                                    else:
                                        item[child_field] = numeric_value
                                except Exception as e:
                                    print(f"[Session {session_id}] ⚠️ Could not convert {parent_field}.{child_field} to numeric: {str(e)}")
                    
                    # Handle double-nested fields (like ppa_details.respondents)
                    elif isinstance(child_fields, dict):
                        for sub_field, sub_child_fields in child_fields.items():
                            if sub_field == "self":
                                # Apply to the item itself
                                for field in sub_child_fields:
                                    if field in item and isinstance(item[field], str):
                                        try:
                                            numeric_value = extract_numeric_value(item[field])
                                            if numeric_value == int(numeric_value):
                                                item[field] = int(numeric_value)
                                            else:
                                                item[field] = numeric_value
                                        except Exception as e:
                                            print(f"[Session {session_id}] ⚠️ Could not convert {parent_field}.{field} to numeric: {str(e)}")
                            elif sub_field in item and isinstance(item[sub_field], list):
                                # Apply to nested list
                                for sub_item in item[sub_field]:
                                    if isinstance(sub_item, dict):
                                        for field in sub_child_fields:
                                            if field in sub_item and isinstance(sub_item[field], str):
                                                try:
                                                    numeric_value = extract_numeric_value(sub_item[field])
                                                    if numeric_value == int(numeric_value):
                                                        sub_item[field] = int(numeric_value)
                                                    else:
                                                        sub_item[field] = numeric_value
                                                except Exception as e:
                                                    print(f"[Session {session_id}] ⚠️ Could not convert {parent_field}.{sub_field}.{field} to numeric: {str(e)}")
    
    return plant_data

def filter_plant_data_to_template(plant_data: dict, session_id: str = "unknown") -> dict:
    """
    Filter plant data to only include fields from the original plant_level.json template
    and convert string values to numeric values for specific fields.
    """
    # Define the exact fields that should be in plant_level.json
    allowed_plant_fields = {
        "sk", "pk", "annual_operational_hours", "auxiliary_power_consumed",
        "closure_year", "commencement_date", "cuf", "gross_power_generation",
        "grid_connectivity_maps", "installed_bess_capacity", "installed_bess_capacity_unit",
        "installed_biomass_capacity", "installed_biomass_capacity_unit",
        "installed_solar_capacity", "installed_solar_capacity_unit",
        "installed_wind_capacity", "installed_wind_capacity_unit",
        "latitude", "longitude", "mandatory_closure", "name", "plant_address",
        "plant_id", "plant_images", "plant_lifetime", "plant_type",
        "potential_reference", "ppa_details", "remaining_useful_life",
        "total_installed_capacity", "total_installed_capacity_unit",
        "metadata",  # Allow metadata as it's added by the system
        # CRITICAL: Add source tracking fields
        "auxiliary_power_consumed_sources", "cuf_sources", "gross_power_generation_sources"
    }
    
    # Filter the data
    filtered_data = {}
    removed_fields = []
    
    # Debug: Check if plant_id exists before filtering
    print(f"[Session {session_id}] 🔍 DEBUG: Before filtering, plant_id = {plant_data.get('plant_id', 'MISSING')}")
    
    for key, value in plant_data.items():
        if key in allowed_plant_fields:
            filtered_data[key] = value
        else:
            removed_fields.append(key)
    
    # Debug: Check if plant_id exists after filtering
    print(f"[Session {session_id}] 🔍 DEBUG: After filtering, plant_id = {filtered_data.get('plant_id', 'MISSING')}")
    
    if removed_fields:
        print(f"[Session {session_id}] 🧹 Removed extra fields from plant data: {removed_fields}")
    
    # Debug: Check plant_id before numeric conversion
    print(f"[Session {session_id}] 🔍 DEBUG: Before numeric conversion, plant_id = {filtered_data.get('plant_id', 'MISSING')}")
    
    # Convert string values to numeric values for specific fields
    filtered_data = convert_numeric_fields(filtered_data, session_id)
    
    # Debug: Check plant_id after numeric conversion
    print(f"[Session {session_id}] 🔍 DEBUG: After numeric conversion, plant_id = {filtered_data.get('plant_id', 'MISSING')}")
    
    return filtered_data

def extract_numeric_value(value):
    """Extract numeric value from various formats"""
    if isinstance(value, (int, float)):
        return float(value)
    
    if isinstance(value, str):
        # Remove common non-numeric characters
        cleaned = value.replace(",", "").replace("MW", "").replace("MWh", "").replace("GW", "").replace("%", "").strip()
        try:
            return float(cleaned)
        except:
            return 0.0
    
    return 0.0

def get_auxiliary_power_percentage(plant_type):
    """Get typical auxiliary power consumption percentage by plant type"""
    auxiliary_power_standards = {
        "solar": 1.0,
        "wind": 1.5,
        "thermal": 5.0,
        "biomass": 6.0,
        "coal": 6.5,
        "gas": 2.5,
        "hydro": 1.0
    }
    return auxiliary_power_standards.get(plant_type, 5.0)

def extract_sources_from_summaries(summaries, field_keywords):
    """Extract source URLs from research summaries for specific fields"""
    sources = []
    if not summaries:
        return sources

    # Split summaries into individual entries
    summary_entries = summaries.split('\n\n') if isinstance(summaries, str) else [str(summaries)]

    for entry in summary_entries:
        # Check if this entry contains relevant field keywords
        entry_lower = entry.lower()
        if any(keyword.lower() in entry_lower for keyword in field_keywords):
            # Extract URLs from this entry
            import re
            urls = re.findall(r'https?://[^\s<>"{}|\\^`\[\]]+', entry)
            sources.extend(urls)

            # Also look for source mentions like "Source: ..." or "From: ..."
            source_patterns = [
                r'source:\s*([^\n]+)',
                r'from:\s*([^\n]+)',
                r'according to\s*([^\n]+)',
                r'reported by\s*([^\n]+)'
            ]
            for pattern in source_patterns:
                matches = re.findall(pattern, entry, re.IGNORECASE)
                sources.extend(matches)

    # Remove duplicates and clean up
    unique_sources = list(set(sources))
    return [source.strip() for source in unique_sources if source.strip()]

# Parallel processing imports would go here when implemented

load_dotenv()

if os.getenv("GEMINI_API_KEY") is None:
    raise ValueError("GEMINI_API_KEY is not set")

# Used for Google Search API
genai_client = Client(api_key=os.getenv("GEMINI_API_KEY"))


def initialize_session(state: OverallState) -> OverallState:
    """Initialize a new research session with proper state management.
    
    This function ensures each research session starts with clean state
    and generates a unique session ID for tracking.
    """
    # Generate a unique session ID
    session_id = str(uuid.uuid4())[:8] + str(int(time.time()))[-4:]
    
    print(f"🚀 INITIALIZE_SESSION CALLED - New research session: {session_id}")
    print(f"🔍 DEBUG: Input state keys: {list(state.keys())}")
    print(f"🔍 DEBUG: Messages count: {len(state.get('messages', []))}")
    print(f"🔍 DEBUG: entity_id in state: {state.get('entity_id', 'NOT_FOUND')}")
    print(f"🔍 DEBUG: Full state entity_id: {state.get('entity_id')}")
    
    # Debug messages in detail
    messages = state.get("messages", [])
    print(f"🔍 DEBUG: Raw messages type: {type(messages)}")
    print(f"🔍 DEBUG: Raw messages content: {messages}")
    
    for i, msg in enumerate(messages):
        msg_type = type(msg).__name__
        msg_content = getattr(msg, 'content', 'NO_CONTENT')
        msg_type_attr = getattr(msg, 'type', 'NO_TYPE')
        print(f"🔍 DEBUG: Message {i}: Type={msg_type}, msg.type={msg_type_attr}, Content='{msg_content}'")
    
    # Get the research topic from messages
    research_topic = get_research_topic(state["messages"]) if state.get("messages") else ""
    
    print(f"📝 Research topic extracted: '{research_topic}'")
    
    # Initialize S3 JSON storage tracking
    plant_s3_urls = get_plant_s3_urls(research_topic, session_id)
    plant_name_sanitized = sanitize_plant_name(research_topic)
    
    # Test S3 connection
    s3_ready = check_s3_connection(session_id)
    
    print(f"🗂️ S3 folder will be: {plant_name_sanitized}")
    print(f"🔗 S3 connection: {'✅ Ready' if s3_ready else '❌ Failed'}")
    print(f"✅ INITIALIZE_SESSION COMPLETE - Session {session_id}")
    
    # Return clean initialized state (no progress messages in UI)
    # CRITICAL: Preserve entity_id for AGI integration
    initialized_state = {
        "messages": state.get("messages", []),
        "session_id": session_id,
        "research_topic": research_topic,  # Store research topic in state
        "search_phase": 1,  # Start with organization-level
        "research_loop_count": 0,
        "web_research_result": [],
        "search_query": [],
        "sources_gathered": [],
        "org_level_complete": False,
        "continue_research": False,
        "phase_complete": False,
        "initial_search_query_count": state.get("initial_search_query_count", 5),
        "max_research_loops": state.get("max_research_loops", 3),
        "reasoning_model": state.get("reasoning_model", ""),
        # S3 JSON Storage initialization
        "plant_name_for_s3": research_topic,
        "s3_json_urls": plant_s3_urls,
        "json_storage_complete": {"organization": False, "plant": False},
        "json_storage_errors": [],
    }

    # CRITICAL: Preserve entity_id if provided (AGI integration)
    if state.get("entity_id"):
        initialized_state["entity_id"] = state["entity_id"]
        print(f"🔧 PRESERVED AGI entity_id in initialized state: {state['entity_id']}")

    return initialized_state


def extract_images_parallel(state: OverallState) -> OverallState:
    """
    LangGraph node that extracts images for the power plant in parallel.
    
    This function runs concurrently with the main research pipeline to gather
    relevant images for the power plant and upload them to S3.
    """
    session_id = state.get("session_id", "unknown")
    research_topic = state.get("research_topic", "")
    
    # Fallback: If research_topic not in state yet, extract from messages
    if not research_topic:
        research_topic = get_research_topic(state.get("messages", []))
        print(f"[Session {session_id}] ⚠️ Image extraction using fallback research topic: '{research_topic}'")
    else:
        print(f"[Session {session_id}] ✅ Image extraction using state research topic: '{research_topic}'")
    
    print(f"[Session {session_id}] 🖼️ Starting parallel image extraction for: {research_topic}")
    
    try:
        # Extract and upload images
        s3_urls = extract_and_upload_images(research_topic, session_id)
        
        if s3_urls:
            print(f"[Session {session_id}] ✅ Image extraction successful: {len(s3_urls)} images uploaded")
            return {
                "image_extraction_complete": True,
                "s3_image_urls": s3_urls,
                "image_extraction_error": ""
            }
        else:
            print(f"[Session {session_id}] ⚠️ No images found or uploaded")
            return {
                "image_extraction_complete": True,
                "s3_image_urls": [],
                "image_extraction_error": "No images found"
            }
            
    except Exception as e:
        error_msg = f"Image extraction failed: {str(e)}"
        print(f"[Session {session_id}] ❌ {error_msg}")
        return {
            "image_extraction_complete": True,
            "s3_image_urls": [],
            "image_extraction_error": error_msg
        }


# Nodes
def generate_query(state: OverallState, config: RunnableConfig) -> QueryGenerationState:
    """LangGraph node that generates search queries based on the User's question."""
    configurable = Configuration.from_runnable_config(config)

    # Check for custom initial search query count
    if state.get("initial_search_query_count") is None:
        state["initial_search_query_count"] = configurable.number_of_initial_queries
    
    # Get the search phase from the state
    search_phase = state.get("search_phase", 1)
    session_id = state.get("session_id", "unknown")
    
    print(f"[Session {session_id}] ===== QUERY GENERATION START =====")
    print(f"[Session {session_id}] Phase: {search_phase}")
    print(f"[Session {session_id}] Current state messages count: {len(state.get('messages', []))}")
    
    # Initialize Gemini 2.0 Flash
    llm = ChatGoogleGenerativeAI(
        model=configurable.query_generator_model,
        temperature=0,
        max_retries=3,
        api_key=os.getenv("GEMINI_API_KEY"),
    )
    structured_llm = llm.with_structured_output(SearchQueryList)

    # Get current date and research topic from state
    current_date = get_current_date()
    research_topic = state.get("research_topic", "")
    if not research_topic:
        # Fallback: extract from messages if not in state
        research_topic = get_research_topic(state["messages"])
        print(f"[Session {session_id}] ⚠️ FALLBACK: Research topic extracted from messages: '{research_topic}'")
    else:
        print(f"[Session {session_id}] ✅ Research topic from state: '{research_topic}'")
    
    # Validate research topic
    if not research_topic or not research_topic.strip():
        print(f"[Session {session_id}] ❌ ERROR: Empty research topic detected!")
        print(f"[Session {session_id}] Messages in state: {len(state.get('messages', []))}")
        for i, msg in enumerate(state.get('messages', [])):
            print(f"[Session {session_id}] Message {i}: {type(msg).__name__} - {str(msg.content)[:100]}")
        
        # Return error queries to prevent infinite loop of bad queries
        return {
            "query_list": [{"query": "ERROR: No research topic provided", "rationale": "Empty research topic"}],
            "research_loop_count": state.get("research_loop_count", 0),
            "number_of_ran_queries": state.get("number_of_ran_queries", 0),
            "search_phase": search_phase,
            "session_id": session_id,  # Preserve session ID
            "research_topic": research_topic,  # Preserve research topic
        }
    
    if search_phase == 1:
        # Organization-level query generation with comprehensive plant discovery
        formatted_prompt = f"""Your goal is to generate sophisticated search queries to gather organization-level information AND discover ALL power plants owned by the organization.

CRITICAL: This is ORGANIZATION-LEVEL extraction. You must find ALL plants owned by the organization, not just the input plant.

Instructions:
- Generate search queries to find detailed information about the power plant named in the research topic.
- MOST IMPORTANT: Generate queries to discover ALL power plants owned by that organization globally.
- Focus on queries that will help extract organization-level information for the org_level.json template including:
  1. country_name: Full official country name where the power plant is located
  2. currency_in: ISO 4217 currency code of that country
  3. financial_year: Fiscal year period in MM-MM format for that country
  4. technology_type: What types of power generation technologies are used (determine if Renewable Energy, Biomass, SMR, Green Hydrogen Plant are enabled true/false)

COMPREHENSIVE PLANT DISCOVERY QUERIES:
- Generate queries to find ALL plants: "[org_name] power plants portfolio", "[org_name] renewable energy projects"
- Search for company reports: "[org_name] annual report", "[org_name] sustainability report"
- Look for regulatory filings: "[org_name] environmental impact assessment", "[org_name] energy projects"
- Find industry databases: "[org_name] wind farms", "[org_name] solar farms", "[org_name] power generation assets"
- Search for press releases: "[org_name] new power plant", "[org_name] energy development"

- Don't produce more than {state.get("initial_search_query_count", 7)} queries (increased for comprehensive search).
- Focus on getting up-to-date information as of {current_date}.

Format:
- Format your response as a JSON object with these exact keys:
   - "rationale": Brief explanation of why these queries are relevant for finding ALL plants
   - "query": A list of search queries (mix of org-level and plant discovery queries)

Research Topic: {research_topic}
"""
    elif search_phase == 2:
        # Plant-level query generation using technology-specific requirements

        # Detect plant technology for targeted queries
        plant_technology = detect_plant_technology(research_topic)
        print(f"[Session {session_id}] 🔧 Generating {plant_technology} plant queries for: {research_topic}")

        # Get technology-specific prompt for context
        tech_context = get_technology_specific_prompt(research_topic, "Unknown", plant_technology)

        formatted_prompt = f"""Your goal is to generate sophisticated search queries to gather plant-level information for a {plant_technology.upper()} power plant according to technology-specific requirements.

PLANT TECHNOLOGY: {plant_technology.upper()}

TECHNOLOGY-SPECIFIC REQUIREMENTS:
{tech_context}

Instructions:
- Generate search queries to find detailed information about the {plant_technology} power plant named in the research topic.
- Focus on queries that will help extract plant-level information for the plant_level.json template AND the technology-specific requirements above including:
  1. name: Official name of the power plant
  2. plant_type: Technology or fuel type
  3. plant_address: District/city, State, Country
  4. latitude/longitude: GPS coordinates (search for "coordinates", "location", "lat long", or use plant address)
  5. total_installed_capacity: Total capacity in MW
  6. commencement_date: Date of commercial operation
  7. plant_lifetime: Plant lifetime in years
  8. remaining_useful_life: End-of-life date
  10. auxiliary_power_consumed: Yearly auxiliary power data
  11. cuf: Capacity Utilization Factor by year
  12. gross_power_generation: Yearly generation data
  13. ppa_details: Power Purchase Agreement information (search for "PPA", "power purchase agreement", "offtake contract", "electricity contract")
  14. grid_connectivity_maps: Grid connection details (search for "transmission", "substation", "grid", "interconnection", "PGCIL")
  15. installed capacity for different technologies (solar, wind, biomass, BESS)
  16. closure_year: When plant is closed down

- Search for multiple plants if they exist under the same organization
- Don't produce more than {state.get("initial_search_query_count", 5)} queries.
- Focus on getting up-to-date information as of {current_date}.

Format: 
- Format your response as a JSON object with these exact keys:
   - "rationale": Brief explanation of why these queries are relevant
   - "query": A list of search queries

Research Topic: {research_topic}
"""
    else:
        # Default case - should not happen in two-phase processing
        formatted_prompt = f"""Generate basic search queries for the research topic: {research_topic}"""
    
    # Generate the search queries with error handling
    try:
        result = structured_llm.invoke(formatted_prompt)
        print(f"[Session {session_id}] Generated {len(result.query)} queries for phase {search_phase}")
        
        # Debug the generated queries
        for i, query_obj in enumerate(result.query):
            if isinstance(query_obj, dict):
                query_text = query_obj.get("query", str(query_obj))
            else:
                query_text = str(query_obj)
            print(f"[Session {session_id}] Query {i+1}: {query_text[:100]}")
        
        # Validate that queries are not all identical nonsense
        query_texts = []
        for query_obj in result.query:
            if isinstance(query_obj, dict):
                query_texts.append(query_obj.get("query", str(query_obj)))
            else:
                query_texts.append(str(query_obj))
        
        # Check for repeated nonsensical queries
        unique_queries = set(query_texts)
        if len(unique_queries) == 1 and "What is the name of the power plant" in list(unique_queries)[0]:
            print(f"[Session {session_id}] ❌ ERROR: LLM generated nonsensical repeated queries!")
            print(f"[Session {session_id}] Research topic was empty, causing bad prompt")
            
            # Return a single error query instead of many bad ones
            return {
                "query_list": [{"query": f"ERROR: Cannot generate queries without research topic. Phase: {search_phase}", "rationale": "Missing research topic"}],
                "research_loop_count": state.get("research_loop_count", 0),
                "number_of_ran_queries": state.get("number_of_ran_queries", 0),
                "search_phase": search_phase,
                "session_id": session_id,  # Preserve session ID
                "research_topic": research_topic,  # Preserve research topic
            }
        
        # Return the query list and preserve state
        return {
            "query_list": result.query,
            "research_loop_count": state.get("research_loop_count", 0),
            "number_of_ran_queries": state.get("number_of_ran_queries", 0),
            "search_phase": search_phase,
            "session_id": session_id,  # Preserve session ID
            "research_topic": research_topic,  # Preserve research topic
        }
        
    except Exception as e:
        print(f"[Session {session_id}] ❌ ERROR in query generation: {str(e)}")
        return {
            "query_list": [{"query": f"ERROR: Query generation failed: {str(e)}", "rationale": "System error"}],
            "research_loop_count": state.get("research_loop_count", 0),
            "number_of_ran_queries": state.get("number_of_ran_queries", 0),
            "search_phase": search_phase,
            "session_id": session_id,  # Preserve session ID
            "research_topic": research_topic,  # Preserve research topic
        }


def web_research(state: WebSearchState, config: RunnableConfig) -> OverallState:
    """LangGraph node that performs web research using the native Google Search API tool."""
    configurable = Configuration.from_runnable_config(config)
    search_phase = state.get("search_phase", 1)
    session_id = state.get("session_id", "unknown")
    
    print(f"[Session {session_id}] ===== WEB RESEARCH START =====")
    print(f"[Session {session_id}] Phase: {search_phase}")
    
    # Get queries from state - could be from query_list or search_query
    queries = state.get("query_list", [])
    if not queries:
        queries = state.get("search_query", [])
    if not queries:
        queries = []
    
    print(f"[Session {session_id}] Queries: {queries}")
    
    # Get the current research topic (should already be extracted in state)
    research_topic = state.get("research_topic", "")
    if not research_topic:
        # Fallback: extract from messages if not in state
        research_topic = get_research_topic(state.get("messages", []))
        print(f"[Session {session_id}] ⚠️ FALLBACK: Research topic extracted from messages: '{research_topic}'")
    else:
        print(f"[Session {session_id}] ✅ Research topic from state: '{research_topic}'")
    
    # Customize the prompt based on the search phase
    if search_phase == 1:
        # Phase 1: Focus on organization-level information AND comprehensive plant discovery
        formatted_prompt = f"""Conduct targeted Google Searches to gather organization-level information AND discover ALL power plants owned by the organization.

CRITICAL: This is an ORGANIZATION-LEVEL search. You must find ALL power plants owned by the organization, not just the input plant.

Instructions:
- The current date is {get_current_date()}.
- STEP 1: Identify the organization that owns the input power plant
- STEP 2: Search comprehensively for ALL power plants owned by that organization globally
- STEP 3: Extract organization-level details according to org_level.json template:
  1. country_name: Full official country name where the organization or plant is located
  2. currency_in: ISO 4217 currency code of that power plant country
  3. financial_year: Fiscal year period in MM-MM format
  4. technology_type: Determine which technologies are used and set enabled true/false for:
     - Renewable Energy (solar, wind, hydro, etc.)
     - Biomass (biomass cofiring, dedicated biomass plants)
     - SMR (Small Modular Reactors)
     - Green Hydrogen Plant (green hydrogen production)

COMPREHENSIVE PLANT DISCOVERY REQUIREMENTS:
- Search for "organization name power plants", "organization name renewable energy portfolio"
- Look for company websites, annual reports, investor presentations
- Search for regulatory filings, environmental impact assessments
- Find press releases about new plant acquisitions or developments
- Look for industry databases and energy sector reports
- Search for "organization name wind farms", "organization name solar farms"
- Check for subsidiary companies and their plants
- Look for joint ventures and partnerships

SEARCH STRATEGY:
- Use multiple search terms: "[org_name] power plants", "[org_name] renewable energy", "[org_name] wind farms", "[org_name] solar projects"
- Search for annual reports and sustainability reports
- Look for regulatory filings and environmental assessments
- Check industry databases and energy sector publications

- Be precise and factual. Only include information from credible sources.
- Track all sources meticulously for citation purposes.

Research Topic: {research_topic}
"""
    elif search_phase == 2:
        # Phase 2: Focus on plant-level information using technology-specific requirements

        # Detect plant technology for targeted search
        plant_technology = detect_plant_technology(research_topic)
        print(f"[Session {session_id}] 🔧 Conducting {plant_technology} plant web search for: {research_topic}")

        formatted_prompt = f"""Conduct targeted Google Searches to gather plant-level information for a {plant_technology.upper()} power plant according to technology-specific requirements.

PLANT TECHNOLOGY: {plant_technology.upper()}

Instructions:
- The current date is {get_current_date()}.
- Search for and extract plant-level details about the {plant_technology} power plant with special focus on {plant_technology}-specific metrics:
  1. name: Official name of the power plant
  2. plant_type: Technology or fuel type
  3. plant_address: District/city, State, Country
  4. latitude/longitude: GPS coordinates (search for "coordinates", "location", "lat long", or use plant address)
  5. total_installed_capacity: Total capacity in MW
  6. commencement_date: Date of commercial operation
  7. plant_lifetime: Plant lifetime in years
  8. remaining_useful_life: End-of-life date
  10. auxiliary_power_consumed: Yearly auxiliary power data
  11. cuf: Capacity Utilization Factor by year
  12. gross_power_generation: Yearly generation data
  13. ppa_details: Power Purchase Agreement information (search for "PPA", "power purchase agreement", "offtake contract", "electricity contract")
  14. grid_connectivity_maps: Grid connection details (search for "transmission", "substation", "grid", "interconnection", "PGCIL")
  15. installed capacity for different technologies (solar, wind, biomass, BESS)
  16. closure_year: When plant is closed down

- Search for multiple plants if they exist under the same organization
- Look for technical documents, regulatory filings, and company reports.
- Be precise and factual. Only include information from credible sources.
- Track all sources meticulously for citation purposes.

Research Topic: {research_topic}
Research Queries: {queries}
"""
    else:
        # Default case
        formatted_prompt = f"""Conduct basic web search for: {research_topic}
Research Queries: {queries}"""

    try:
        # Uses the google genai client
        response = genai_client.models.generate_content(
            model=configurable.query_generator_model,
            contents=formatted_prompt,
            config={
                "tools": [{"google_search": {}}],
                "temperature": 0,
            },
        )
        
        # Get grounding chunks safely
        grounding_chunks = None
        if (response and hasattr(response, 'candidates') and response.candidates 
            and hasattr(response.candidates[0], 'grounding_metadata') 
            and response.candidates[0].grounding_metadata
            and hasattr(response.candidates[0].grounding_metadata, 'grounding_chunks')):
            grounding_chunks = response.candidates[0].grounding_metadata.grounding_chunks
        
        # resolve the urls to short urls for saving tokens and time
        resolved_urls = resolve_urls(grounding_chunks, session_id)
        
        # Gets the citations and adds them to the generated text
        citations = get_citations(response, resolved_urls)
        modified_text = insert_citation_markers(response.text, citations)
        sources_gathered = [item for citation in citations for item in citation["segments"]]
        
        print(f"[Session {session_id}] Successfully completed web research for phase {search_phase}")
        
    except Exception as e:
        import traceback
        print(f"[Session {session_id}] Error in web_research: {str(e)}")
        print(f"[Session {session_id}] Traceback: {traceback.format_exc()}")
        modified_text = f"I encountered an error while researching '{research_topic}'. Error: {str(e)}"
        sources_gathered = []

    return {
        "sources_gathered": sources_gathered,
        "search_query": queries,
        "web_research_result": [modified_text],
        "search_phase": state.get("search_phase"),
    }


def reflection(state: OverallState, config: RunnableConfig) -> ReflectionState:
    """LangGraph node that identifies knowledge gaps and generates potential follow-up queries."""
    configurable = Configuration.from_runnable_config(config)
    state["research_loop_count"] = state.get("research_loop_count", 0) + 1
    reasoning_model = state.get("reasoning_model") or configurable.reasoning_model
    search_phase = state.get("search_phase", 1)
    
    current_date = get_current_date()
    session_id = state.get("session_id", "unknown")
    
    print(f"[Session {session_id}] ===== REFLECTION START =====")
    print(f"[Session {session_id}] Phase: {search_phase}")
    print(f"[Session {session_id}] Research loop count: {state['research_loop_count']}")
    
    # Combine all web research results so far
    all_summaries = "\n\n".join(state.get("web_research_result", []))
    
    # Format the prompt based on the search phase
    if search_phase == 1:
        formatted_prompt = f"""Analyze the organization-level research findings and determine if additional research is needed.

Current findings:
{all_summaries}

Instructions:
- Review the research findings for organization-level information according to org_level.json template
- Identify any missing critical organization-level fields
- If important organization-level information is missing, generate 1-2 specific follow-up queries
- If sufficient organization-level information has been gathered, indicate research is complete

Current date: {current_date}
Max research loops: {state.get("max_research_loops", 3)}
Current loop: {state["research_loop_count"]}

Required organization fields to check:
- country_name: Full official country name
- currency_in: ISO 4217 currency code
- financial_year: Fiscal year period in MM-MM format  
- technology_type: Enabled status for Renewable Energy, Biomass, SMR, Green Hydrogen Plant
"""
    elif search_phase == 2:
        formatted_prompt = f"""Analyze the plant-level research findings and determine if additional research is needed.

Current findings:
{all_summaries}

Instructions:
- Review the research findings for plant-level information according to plant_level.json template
- Identify any missing critical plant-level fields
- If important plant-level information is missing, generate 1-2 specific follow-up queries
- If sufficient plant-level information has been gathered, indicate research is complete

Current date: {current_date}
Max research loops: {state.get("max_research_loops", 3)}
Current loop: {state["research_loop_count"]}

Required plant fields to check:
- name, plant_type, plant_address, latitude/longitude
- total_installed_capacity, commencement_date
- auxiliary_power_consumed, cuf, gross_power_generation
- ppa_details, grid_connectivity_maps
"""
    else:
        formatted_prompt = f"""Analyze the research findings and determine if additional research is needed: {all_summaries}"""

    llm = ChatGoogleGenerativeAI(
        model=reasoning_model,
        temperature=0,
        max_retries=3,
        api_key=os.getenv("GEMINI_API_KEY"),
    )
    structured_llm = llm.with_structured_output(Reflection)

    try:
        result = structured_llm.invoke(formatted_prompt)
        
        # Add null check for result
        if result is None:
            print(f"[Session {session_id}] Warning: LLM returned None result")
            continue_research = False
            search_query = []
        else:
            # Check if follow_up_queries exists and is not None
            has_follow_up = hasattr(result, 'follow_up_queries') and result.follow_up_queries is not None
            continue_research = bool(has_follow_up and state["research_loop_count"] < state.get("max_research_loops", 3))
            search_query = [result.follow_up_queries[0]] if has_follow_up and result.follow_up_queries else []
    except Exception as e:
        print(f"[Session {session_id}] Error in reflection: {str(e)}")
        continue_research = False
        search_query = []
    
    print(f"[Session {session_id}] Continue research: {continue_research}")
    
    return {
        "continue_research": continue_research,
        "search_query": search_query,
        "research_loop_count": state["research_loop_count"],
        "search_phase": search_phase,
    }


def finalize_answer_parallel(state: OverallState, config: RunnableConfig):
    """Enhanced LangGraph node that performs parallel image extraction and JSON processing."""
    configurable = Configuration.from_runnable_config(config)
    
    # Initialize LLM
    llm = ChatGoogleGenerativeAI(
        model=configurable.reasoning_model,
        temperature=0,
        max_retries=3,
        api_key=os.getenv("GEMINI_API_KEY"),
    )
    
    # Get basic info
    current_date = get_current_date()
    session_id = state.get("session_id", "unknown")
    search_phase = state.get("search_phase", 1)
    
    research_topic = state.get("research_topic", "")
    # Fallback: If research_topic not in state, extract from messages
    if not research_topic:
        research_topic = get_research_topic(state["messages"])
        print(f"[Session {session_id}] ⚠️ Finalize using fallback research topic: '{research_topic}'")
    
    print(f"[Session {session_id}] ===== FINALIZE ANSWER START =====")
    print(f"[Session {session_id}] Phase: {search_phase}")
    print(f"[Session {session_id}] Research topic: {research_topic}")
    
    # Combine all research summaries
    summaries = "\n\n".join(state.get("web_research_result", []))
    
    if search_phase == 1:
        # Organization-level extraction according to org_level.json template
        org_prompt = f"""Extract and format organization-level information according to the org_level.json template.

Instructions:
- The current date is {current_date}.
- Extract organization-level information from the research summaries.
- Format response EXACTLY as the org_level.json template with these fields:

{{
  "pk": null,
  "sk": "scraped#org_details",
  "country_name": "full name of the country where the organization or plant is located",
  "currency_in": "ISO 4217 currency code of that power plant country",
  "financial_year": "Fiscal year period in MM-MM format",
  "currency_convert_to": null,
  "currency_listed": [],
  "technology_type": [
    {{
      "enabled": "true/false",
      "name": "Renewable Energy"
    }},
    {{
      "enabled": "true/false",
      "name": "Biomass"
    }},
    {{
      "enabled": "true/false",
      "name": "SMR"
    }},
    {{
      "enabled": "true/false",
      "name": "Green Hydrogen Plant"
    }}
  ]
}}

- For technology_type array, set "enabled" to "true" if that technology is used by the organization, "false" if not
- Keep static fields exactly as shown: "pk": null, "currency_convert_to": null, "currency_listed": []
- If information is not available, use empty strings for text fields
- Ensure JSON is properly formatted and valid
- Do not include explanatory text before or after the JSON
- Use ONLY the fields shown above, in this exact structure

Research Topic: {research_topic}

Summaries:
{summaries}
"""
        try:
            result = llm.invoke(org_prompt)
            final_content = result.content
            
            # Process organization data for S3 storage
            import json
            
            try:
                # Extract JSON from response
                print(f"[Session {session_id}] 🔍 Extracting organization JSON from response...")
                print(f"[Session {session_id}] Response length: {len(final_content)}")
                print(f"[Session {session_id}] First 200 chars: {final_content[:200]}")
                
                start_idx = final_content.find('{')
                end_idx = final_content.rfind('}') + 1
                print(f"[Session {session_id}] JSON boundaries: start={start_idx}, end={end_idx}")
                
                if start_idx != -1 and end_idx != -1:
                    json_str = final_content[start_idx:end_idx]
                    print(f"[Session {session_id}] Extracted JSON: {json_str[:200]}...")
                    
                    org_data = json.loads(json_str)
                    print(f"[Session {session_id}] ✅ JSON parsed successfully")
                    
                    # Set static sk field for organization level
                    org_data["sk"] = "scraped#org_details"

                    # Add required organization fields
                    # Get organization name from discovered data or state
                    discovered_org_name = state.get("discovered_org_name", "")
                    if discovered_org_name:
                        org_data["organization_name"] = discovered_org_name
                    elif not org_data.get("organization_name"):
                        org_data["organization_name"] = "Not available"

                    # Add selected_state field (set to null as requested)
                    org_data["selected_state"] = None

                    print(f"[Session {session_id}] 🏢 Added organization_name: {org_data.get('organization_name')}")
                    print(f"[Session {session_id}] 🏢 Added selected_state: {org_data.get('selected_state')}")

                    # Convert any numeric fields in organization data
                    org_data = convert_org_numeric_fields(org_data, session_id)
                    
                    # Store organization data to S3
                    current_s3_urls = state.get("s3_json_urls", {})
                    current_storage_status = state.get("json_storage_complete", {})
                    current_errors = state.get("json_storage_errors", [])
                    
                    plant_name_for_s3 = state.get("plant_name_for_s3", research_topic)
                    org_uid = state.get("org_uid", "")
                    print(f"[Session {session_id}] 🏭 Storing organization data for: {plant_name_for_s3}")
                    print(f"[Session {session_id}] 🔑 Using org_uid: {org_uid}")

                    org_s3_url = store_organization_data(org_data, plant_name_for_s3, session_id, org_uid)

                    # CRITICAL: Save AGI organization data to database if entity_id is present
                    entity_id = state.get("entity_id")
                    if entity_id and org_uid:
                        print(f"[Session {session_id}] 🔧 AGI Integration: Saving org data to database")
                        from agent.database_manager import get_database_manager
                        db_manager = get_database_manager()
                        db_success = db_manager.save_agi_organization_data(
                            plant_name=plant_name_for_s3,
                            org_uid=org_uid,
                            org_name=org_data.get("organization_name", ""),
                            country=org_data.get("country_name", ""),
                            session_id=session_id
                        )
                        if db_success:
                            print(f"[Session {session_id}] ✅ AGI organization data saved to database")
                        else:
                            print(f"[Session {session_id}] ❌ Failed to save AGI organization data to database")

                    if org_s3_url:
                        print(f"[Session {session_id}] ✅ Organization JSON saved to S3: {org_s3_url}")
                        current_s3_urls["organization"] = org_s3_url
                        current_storage_status["organization"] = True
                    else:
                        print(f"[Session {session_id}] ❌ Failed to save organization JSON to S3")
                        current_errors.append("Failed to save organization JSON to S3")
                    
                    # Update final content with processed JSON
                    updated_json = json.dumps(org_data, indent=2)
                    final_content = f"ORGANIZATION-LEVEL INFORMATION:\n{updated_json}"
                else:
                    print(f"[Session {session_id}] ❌ No valid JSON found in organization response")
                    current_errors.append("No valid JSON found in organization response")
                    
            except Exception as e:
                print(f"⚠️ Could not process organization JSON: {e}")
                import traceback
                print(f"[Session {session_id}] Organization JSON processing error: {traceback.format_exc()}")
                current_s3_urls = state.get("s3_json_urls", {})
                current_storage_status = state.get("json_storage_complete", {})
                current_errors = state.get("json_storage_errors", [])
                current_errors.append(f"Organization-level extraction failed: {str(e)}")
            
            print("Successfully generated organization-level information")
            
        except Exception as e:
            import traceback
            print(f"Error in organization-level generation: {str(e)}")
            print(traceback.format_exc())
            
            # Fallback organization response
            final_content = f"""ORGANIZATION-LEVEL INFORMATION:
{{
  "pk": null,
  "sk": "scraped#org_details",
  "country_name": "",
  "currency_in": "",
  "financial_year": "", 
  "currency_convert_to": null,
  "currency_listed": [],
  "technology_type": [
    {{
      "enabled": "false",
      "name": "Renewable Energy"
    }},
    {{
      "enabled": "false",
      "name": "Biomass"
    }},
    {{
      "enabled": "false",
      "name": "SMR"
    }},
    {{
      "enabled": "false",
      "name": "Green Hydrogen Plant"
    }}
  ]
}}"""
            
            current_s3_urls = state.get("s3_json_urls", {})
            current_storage_status = state.get("json_storage_complete", {})
            current_errors = state.get("json_storage_errors", [])
            current_errors.append(f"Organization-level extraction failed: {str(e)}")
            
            print("Using fallback organization-level information")

        # Filter sources used in the response
        unique_sources = []
        for source in state.get("sources_gathered", []):
            if isinstance(source, dict):
                if ("short_url" in source and source["short_url"] in final_content) or \
                   ("value" in source and source["value"] in final_content):
                    unique_sources.append(source)
            elif hasattr(source, "short_url") and source.short_url in final_content:
                unique_sources.append(source)
            elif hasattr(source, "value") and source.value in final_content:
                unique_sources.append(source)

        # Return organization-level results
        print(f"[Session {session_id}] 📋 Final organization state:")
        print(f"[Session {session_id}] - S3 URLs: {current_s3_urls}")
        print(f"[Session {session_id}] - Storage status: {current_storage_status}")
        print(f"[Session {session_id}] - Errors: {current_errors}")
        
        return {
            "messages": [AIMessage(content=final_content)],
            "sources_gathered": unique_sources,
            "s3_json_urls": current_s3_urls,
            "json_storage_complete": current_storage_status,
            "json_storage_errors": current_errors,
        }
    
    elif search_phase == 2:
        # Plant-level extraction using technology-specific prompts

        # Detect plant technology from research topic and summaries
        plant_technology = detect_plant_technology(research_topic, {"summaries": summaries})
        print(f"[Session {session_id}] 🔧 Detected plant technology: {plant_technology}")

        # Get technology-specific detailed prompt
        tech_specific_prompt = get_technology_specific_prompt(research_topic, "Unknown", plant_technology)

        plant_prompt = f"""Extract and format plant-level information using technology-specific requirements.

TECHNOLOGY-SPECIFIC DATA REQUIREMENTS:
{tech_specific_prompt}

EXTRACTION INSTRUCTIONS:
- The current date is {current_date}.
- CAREFULLY ANALYZE the research summaries to identify ALL distinct power plants.
- IMPORTANT: Thoroughly search for mentions of multiple plants, locations, or units that indicate separate facilities.
- If there are multiple power plants mentioned, you MUST return an array of plant objects, one for each distinct plant.
- If there is only one power plant, return a single plant object.
- For each plant, extract ALL information according to the plant_level.json template AND the technology-specific requirements above:

MULTIPLE PLANT DETECTION GUIDELINES:
- Look for phrases like "X MW plant in [Location 1] and Y MW plant in [Location 2]"
- Check for different geographical locations that indicate separate plants
- Pay attention to distinct commissioning dates for different facilities
- Note different capacities mentioned for separate locations
- Consider mentions of "plants" (plural) or "multiple sites"

IMPORTANT RESPONSE FORMAT:
- If you identify MULTIPLE plants, you MUST return a JSON ARRAY with multiple plant objects: [{ "first_plant_data" }, { "second_plant_data" }]
- If you identify only ONE plant, return a single JSON object: { "single_plant_data" }

NUMERIC VALUES:
- All numeric values MUST be returned as numbers without quotes
- Examples: 
  * CORRECT: "installed_solar_capacity": 100.5
  * INCORRECT: "installed_solar_capacity": "100.5"
- The following fields MUST be numeric (not strings):
  * annual_operational_hours
  * auxiliary_power_consumed.value
  * cuf.value
  * gross_power_generation.value
  * installed_bess_capacity
  * installed_biomass_capacity
  * installed_solar_capacity
  * installed_wind_capacity
  * latitude
  * longitude
  * ppa_details.capacity
  * ppa_details.respondents.capacity
  * ppa_details.respondents.price
  * total_installed_capacity

Format each plant object EXACTLY as the plant_level.json template with these fields:

{{
  "sk": "plant#[plant_type]#[plant_id]",
  "pk": null,
  "annual_operational_hours": 8760,
  "auxiliary_power_consumed": [
    {{
      "value": "The auxiliary (AUX) energy percentage",
      "year": "Year for which auxiliary power data is reported"
    }}
  ],
  "closure_year": "when the plant is closed down",
  "commencement_date": "The date of commercial operation (yyyy-mm-ddThh:mm:ss.000Z)",
  "cuf": [
    {{
      "value": "cuf value for the plant",
      "year": 2020
    }}
  ],
  "gross_power_generation": [
    {{
      "value": "Total energy generated by the plant",
      "year": "Year of the power generation data"
    }}
  ],
  "grid_connectivity_maps": [
    {{
      "details": [
        {{
          "capacity": "The rated capacity of the connection",
          "latitude": "The geographic latitude of the substation",
          "longitude": "The geographic longitude of the substation",
          "projects": [
            {{
              "distance": "The distance from the substation to project"
            }}
          ],
          "substation_name": "The official name of the substation",
          "substation_type": "The classification and voltage level of the substation"
        }}
      ],
      "name": "",
      "s3_url": ""
    }}
  ],
  "installed_bess_capacity": "installed battery capacity of the plant",
  "installed_bess_capacity_unit": "MWh",
  "installed_biomass_capacity": "installed biomass capacity of the plant",
  "installed_biomass_capacity_unit": "MW",
  "installed_solar_capacity": "installed solar capacity of the plant",
  "installed_solar_capacity_unit": "MW",
  "installed_wind_capacity": "installed wind capacity of the plant",
  "installed_wind_capacity_unit": "MW",
  "latitude": "Latitude coordinates in decimal degrees (e.g., 14.3456 or use Google Maps to find coordinates if address is known)",
  "longitude": "Longitude coordinates in decimal degrees (e.g., 79.7890 or use Google Maps to find coordinates if address is known)",
  "mandatory_closure": "",
  "name": "name of the power plant",
  "plant_address": "District or city, State, Country",
  "plant_id": "sequential number starting from 1",
  "plant_images": [],
  "plant_lifetime": 25,
  "plant_type": "The technology or fuel type of the plant",
  "potential_reference": {{
    "lat": null,
    "long": null
  }},
  "ppa_details": [
    {{
      "capacity": "The capacity covered by this PPA",
      "capacity_unit": "The unit of that capacity",
      "end_date": "The PPA's termination date (YYYY-MM-DD)",
      "respondents": [
        {{
          "capacity": "The capacity volume contracted by this respondent",
          "currency": "The currency in which the price is denominated",
          "name": "The counterparty's name",
          "price": "The contracted price per unit of energy",
          "price_unit": "The basis for the price"
        }}
      ],
      "start_date": "The PPA's commencement date (YYYY-MM-DD)",
      "tenure": "The numeric duration of the PPA",
      "tenure_type": "The unit for the tenure"
    }}
  ],
  "remaining_useful_life": "The end-of-life date (yyyy-mm-ddThh:mm:ss.000Z)",
  "total_installed_capacity": "Total installed capacity of the plant in MW",
  "total_installed_capacity_unit": "MW"
}}

- Format response as either a single JSON object or array of JSON objects
- If organization has multiple plants, create separate JSON objects with sequential plant_id (1, 2, 3, etc.)
- Do not include explanatory text before or after the JSON
- If information is not available, use empty strings, empty arrays, or appropriate defaults
- Ensure JSON is properly formatted and valid
- For coordinates: If you find the plant address/location, try to provide approximate coordinates
- For PPA details: Carefully extract ALL nested fields:
  * capacity, capacity_unit, start_date, end_date, tenure, tenure_type
  * For each respondent: name, capacity, currency, price, price_unit
  * Search for pricing information, contract durations, and counterparty details
- For grid connectivity: Extract complete nested structure:
  * substation_name, substation_type, capacity, latitude, longitude
  * For projects array: distance from substation
  * Search for transmission line details, voltage levels, and connection points

IMPORTANT CAPACITY FIELD LOGIC:
- Set capacity fields based on plant_type:
  * If plant_type is "solar": Set installed_solar_capacity to actual value, others to ""
  * If plant_type is "wind": Set installed_wind_capacity to actual value, others to ""
  * If plant_type is "biomass": Set installed_biomass_capacity to actual value, others to ""
  * If plant_type is "thermal": Set all renewable capacity fields to ""
  * If plant_type is "hybrid": Set multiple capacity fields as appropriate

FALLBACK CALCULATIONS FOR MISSING DATA:
- For auxiliary_power_consumed: If not found in web search, calculate for last 5 years (2020-2024) using industry standards
- For cuf: If not found in web search, calculate using formula: (Annual electricity generated)/(plant capacity*8760) for last 5 years
- For gross_power_generation: Provide data for last 5 years (2020-2024) if available
- Provide data for last 5 years (2020, 2021, 2022, 2023, 2024) with estimated values if actual data not available

CRITICAL TIME SERIES FORMAT REQUIREMENTS:
- ALL time series fields (auxiliary_power_consumed, cuf, gross_power_generation, etc.) must use:
  * DECIMAL VALUES (not percentages): e.g., 0.005 instead of 0.5% or "0.5%"
  * SIMPLE YEAR FORMAT: "2020", "2021", "2022", "2023", "2024" (not "2020-21" or "2022-23")
  * EXACTLY 5 YEARS: Always provide data for 2020, 2021, 2022, 2023, 2024
- UNITS SPECIFICATION:
  * auxiliary_power_consumed: Decimal fraction (e.g., 0.005 for 0.5%)
  * cuf: Decimal fraction (e.g., 0.25 for 25%)
  * gross_power_generation: MU (Million Units) as decimal (e.g., 15.250 MU)
- Example format: {{"value": 0.005, "year": "2022"}}

CRITICAL SOURCE TRACKING REQUIREMENTS - MANDATORY:
- For auxiliary_power_consumed, cuf, and gross_power_generation fields, you MUST ALWAYS include source information:
  * "auxiliary_power_consumed_sources": ["list of URLs/documents where auxiliary power data was found"]
  * "cuf_sources": ["list of URLs/documents where CUF data was found"]
  * "gross_power_generation_sources": ["list of URLs/documents where generation data was found"]
- If data is estimated/calculated, use source: "Calculated using industry standards"
- If no specific source found, use: "Estimated based on plant type and capacity"
- NEVER omit these source fields - they are REQUIRED in every plant JSON
- Example: "auxiliary_power_consumed_sources": ["https://example.com/report.pdf", "Company annual report 2023"]

Research Topic: {research_topic}

FINAL REMINDER:
- CAREFULLY check if there are MULTIPLE distinct plants mentioned in the summaries
- If multiple plants exist, you MUST return a JSON ARRAY: [{"plant_data_1"}, {"plant_data_2"}]
- Each plant should have its own complete set of data
- Different locations, capacities, or commissioning dates usually indicate separate plants

Summaries:
{summaries}
"""
        try:
            print(f"[Session {session_id}] 🔍 DEBUG: About to invoke LLM for plant-level processing")
            print(f"[Session {session_id}] 🔍 DEBUG: Summaries length: {len(summaries)}")
            print(f"[Session {session_id}] 🔍 DEBUG: Research topic: {research_topic}")
            print(f"[Session {session_id}] 🔍 DEBUG: First 200 chars of summaries: {summaries[:200]}")

            result = llm.invoke(plant_prompt)
            final_content = result.content if result else ""

            print(f"[Session {session_id}] 🔍 DEBUG: LLM result type: {type(result)}")
            print(f"[Session {session_id}] 🔍 DEBUG: LLM result content length: {len(final_content)}")
            print(f"[Session {session_id}] 🔍 DEBUG: First 200 chars of result: {final_content[:200] if final_content else 'EMPTY'}")
            
            # Process plant data with multiple plant support
            import json
            
            plant_name_for_s3 = state.get("plant_name_for_s3", research_topic)
            
            # Try to extract JSON from response
            plants_data = None
            json_str = ""
            
            def extract_json_safely(content):
                """Extract JSON from content using multiple strategies."""
                import re
                
                # First, check if the content contains an array indicator
                contains_array = "[" in content and "]" in content
                print(f"[Session {session_id}] 🔍 JSON contains array indicators: {contains_array}")
                
                # Strategy 1: Look for array format first (prioritize arrays for multiple plants)
                array_pattern = r'\[[\s\S]*\]'
                array_matches = re.findall(array_pattern, content)
                
                for match in array_matches:
                    try:
                        data = json.loads(match)
                        if isinstance(data, list):
                            print(f"[Session {session_id}] ✅ Successfully extracted array with {len(data)} items")
                            return data, match
                        else:
                            print(f"[Session {session_id}] ⚠️ Found array-like structure but it's not a list")
                            return [data], match
                    except json.JSONDecodeError:
                        continue
                
                # Strategy 2: Look for object format
                obj_pattern = r'\{[\s\S]*\}'
                obj_matches = re.findall(obj_pattern, content)
                
                for match in obj_matches:
                    try:
                        data = json.loads(match)
                        print(f"[Session {session_id}] ✅ Extracted single object (no array found)")
                        return [data], match
                    except json.JSONDecodeError:
                        continue
                
                # Strategy 3: Clean and try parsing the whole content
                try:
                    # Remove common prefixes/suffixes
                    cleaned = content.strip()
                    if cleaned.startswith('```json'):
                        cleaned = cleaned[7:]
                    if cleaned.endswith('```'):
                        cleaned = cleaned[:-3]
                    
                    # Try parsing cleaned content
                    data = json.loads(cleaned.strip())
                    if isinstance(data, list):
                        print(f"[Session {session_id}] ✅ Extracted array with {len(data)} items after cleaning")
                        return data, cleaned
                    else:
                        print(f"[Session {session_id}] ✅ Extracted single object after cleaning")
                        return [data], cleaned
                except json.JSONDecodeError:
                    print(f"[Session {session_id}] ⚠️ Could not parse JSON after cleaning")
                    pass
                
                # Strategy 4: Try to find and fix common JSON errors
                try:
                    # Look for potential JSON objects or arrays with minor formatting issues
                    if "[" in content and "]" in content:
                        # Try to extract content between outermost [ and ]
                        start_idx = content.find("[")
                        end_idx = content.rfind("]") + 1
                        if start_idx != -1 and end_idx != -1:
                            potential_array = content[start_idx:end_idx]
                            # Try to fix common issues like trailing commas
                            potential_array = re.sub(r',\s*]', ']', potential_array)
                            data = json.loads(potential_array)
                            if isinstance(data, list):
                                print(f"[Session {session_id}] ✅ Extracted array with {len(data)} items after fixing")
                                return data, potential_array
                except Exception as e:
                    print(f"[Session {session_id}] ⚠️ Error in JSON repair attempt: {str(e)}")
                    pass
                
                print(f"[Session {session_id}] ❌ Failed to extract any valid JSON")
                return None, ""
            
            plants_data, json_str = extract_json_safely(final_content)
            
            print(f"[Session {session_id}] 🔍 JSON extraction debug:")
            print(f"[Session {session_id}] Content length: {len(final_content)}")
            print(f"[Session {session_id}] First 200 chars: {final_content[:200]}")
            print(f"[Session {session_id}] JSON string found: {bool(json_str)}")
            print(f"[Session {session_id}] Plants data found: {bool(plants_data)}")
            
            if plants_data:
                print(f"[Session {session_id}] 🏭 Number of plants detected: {len(plants_data)}")
                print(f"[Session {session_id}] 🔍 Plant detection details:")
                for idx, plant in enumerate(plants_data):
                    plant_name = plant.get("name", "Unknown")
                    plant_type = plant.get("plant_type", "Unknown")
                    plant_location = plant.get("plant_address", "Unknown location")
                    plant_capacity = plant.get("total_installed_capacity", "Unknown capacity")
                    print(f"[Session {session_id}] 🏭 Plant {idx+1}: {plant_name} ({plant_type}) - {plant_capacity} at {plant_location}")
            else:
                print(f"[Session {session_id}] ⚠️ No plant data could be extracted from the response")
            
            if plants_data and json_str:
                # Process each plant and assign sequential plant_id
                processed_plants = []
                image_urls = state.get("s3_image_urls", [])
                
                print(f"[Session {session_id}] 🏭 PROCESSING {len(plants_data)} PLANTS")
                
                for idx, plant_data in enumerate(plants_data):
                    # Assign sequential plant_id starting from 1
                    plant_id = idx + 1
                    plant_data["plant_id"] = plant_id
                    print(f"[Session {session_id}] 🔍 DEBUG: Setting plant_id to {plant_id} for plant {idx+1}")
                    print(f"[Session {session_id}] 🏭 PLANT {plant_id} NAME: {plant_data.get('name', 'Unknown')}")
                    
                    # Generate sk field according to template format
                    plant_type = plant_data.get("plant_type", "Unknown")
                    sk_value = f"plant#{plant_type}#{plant_id}"
                    plant_data["sk"] = sk_value
                    
                    # Add image URLs to each plant
                    if image_urls:
                        # Store image URLs in the plant data
                        plant_data["plant_images"] = image_urls
                        print(f"[Session {session_id}] ✅ Added {len(image_urls)} image URLs to plant {plant_id}")
                    
                    # Ensure all required template fields exist with defaults
                    template_defaults = {
                        "pk": "default null",
                        "annual_operational_hours": 8760,
                        "auxiliary_power_consumed": [],
                        "closure_year": "",
                        "commencement_date": "",
                        "cuf": [],
                        "gross_power_generation": [],
                        "grid_connectivity_maps": [],
                        "installed_bess_capacity": "",
                        "installed_bess_capacity_unit": "MWh",
                        "installed_biomass_capacity": "",
                        "installed_biomass_capacity_unit": "MW",
                        "installed_solar_capacity": "",
                        "installed_solar_capacity_unit": "MW",
                        "installed_wind_capacity": "",
                        "installed_wind_capacity_unit": "MW",
                        "latitude": "",
                        "longitude": "",
                        "mandatory_closure": "",
                        "plant_images": [],
                        "plant_lifetime": 25,
                        "potential_reference": {"lat": None, "long": None},
                        "ppa_details": [],
                        "remaining_useful_life": "",
                        "total_installed_capacity": "",
                        "total_installed_capacity_unit": "MW"
                        
                    }
                    
                    # Apply defaults for missing fields
                    for key, default_value in template_defaults.items():
                        if key not in plant_data:
                            plant_data[key] = default_value
                    
                    processed_plants.append(plant_data)
                    print(f"✅ Processed plant {plant_id}: {plant_data.get('name', 'Unknown')}")
                
                # Update final_content with processed plants
                if len(processed_plants) == 1:
                    updated_json = json.dumps(processed_plants[0], indent=2)
                else:
                    updated_json = json.dumps(processed_plants, indent=2)
                
                final_content = f"PLANT-LEVEL INFORMATION:\n{updated_json}"
                print(f"Successfully generated plant-level information for {len(processed_plants)} plant(s)")
                
                # Store each plant as separate JSON file
                current_s3_urls = state.get("s3_json_urls", {})
                current_storage_status = state.get("json_storage_complete", {})
                current_errors = state.get("json_storage_errors", [])
                
                for plant_data in processed_plants:
                    plant_id = plant_data["plant_id"]
                    plant_name = plant_data.get("name", f"Plant_{plant_id}")
                    
                    # Apply simple plant-level fallback calculations for missing data
                    try:
                        print(f"[Session {session_id}] 🔧 Applying plant-level fallback calculations for Plant {plant_id}")

                        plant_capacity = extract_numeric_value(plant_data.get("total_installed_capacity", "0"))
                        plant_type = plant_data.get("plant_type", "thermal").lower()

                        # STEP 1: Extract sources from research summaries for time series fields
                        print(f"[Session {session_id}] 🔍 Extracting sources for time series data...")

                        # Extract sources for auxiliary power consumption
                        if not plant_data.get("auxiliary_power_consumed_sources"):
                            aux_sources = extract_sources_from_summaries(summaries,
                                ["auxiliary power", "auxiliary consumption", "station consumption", "parasitic load"])
                            if aux_sources:
                                plant_data["auxiliary_power_consumed_sources"] = aux_sources
                                print(f"[Session {session_id}] ✅ Found {len(aux_sources)} sources for auxiliary power data")

                        # Extract sources for CUF
                        if not plant_data.get("cuf_sources"):
                            cuf_sources = extract_sources_from_summaries(summaries,
                                ["capacity utilization", "CUF", "capacity factor", "plant load factor", "PLF"])
                            if cuf_sources:
                                plant_data["cuf_sources"] = cuf_sources
                                print(f"[Session {session_id}] ✅ Found {len(cuf_sources)} sources for CUF data")

                        # Extract sources for gross power generation
                        if not plant_data.get("gross_power_generation_sources"):
                            gen_sources = extract_sources_from_summaries(summaries,
                                ["power generation", "electricity generation", "energy generation", "annual generation", "MWh", "MU"])
                            if gen_sources:
                                plant_data["gross_power_generation_sources"] = gen_sources
                                print(f"[Session {session_id}] ✅ Found {len(gen_sources)} sources for generation data")

                        # CRITICAL: Ensure ALL time series fields have sources (force add if missing)
                        if plant_data.get("auxiliary_power_consumed") and not plant_data.get("auxiliary_power_consumed_sources"):
                            plant_data["auxiliary_power_consumed_sources"] = ["Data extracted from web research summaries"]
                            print(f"[Session {session_id}] 🔧 Force-added auxiliary power sources")

                        if plant_data.get("cuf") and not plant_data.get("cuf_sources"):
                            plant_data["cuf_sources"] = ["Data extracted from web research summaries"]
                            print(f"[Session {session_id}] 🔧 Force-added CUF sources")

                        if plant_data.get("gross_power_generation") and not plant_data.get("gross_power_generation_sources"):
                            plant_data["gross_power_generation_sources"] = ["Data extracted from web research summaries"]
                            print(f"[Session {session_id}] 🔧 Force-added generation sources")
                        
                        # Add fallback auxiliary power consumption if missing (5 years: 2020-2024)
                        if not plant_data.get("auxiliary_power_consumed"):
                            aux_percentage = get_auxiliary_power_percentage(plant_type)
                            # Convert percentage to decimal (e.g., 1.0% -> 0.01)
                            aux_decimal = aux_percentage / 100.0
                            plant_data["auxiliary_power_consumed"] = [
                                {"value": aux_decimal, "year": str(year)}
                                for year in [2020, 2021, 2022, 2023, 2024]
                             ]
                            # Add source tracking for fallback data
                            plant_data["auxiliary_power_consumed_sources"] = [
                                f"Calculated using industry standards for {plant_type} plants ({aux_percentage}% auxiliary power consumption)"
                            ]
                            print(f"[Session {session_id}] ✅ Added fallback auxiliary power data (5 years, decimal format) with source tracking")
                        
                        # Calculate CUF if missing but gross_power_generation is available (5 years: 2020-2024)
                        if not plant_data.get("cuf") and plant_data.get("gross_power_generation") and plant_capacity > 0:
                            cuf_data = []
                            for gen_record in plant_data["gross_power_generation"]:
                                if isinstance(gen_record, dict) and "value" in gen_record:
                                    # Extract MU from generation value and convert to MWh
                                    gen_mu = extract_numeric_value(gen_record["value"])
                                    gen_mwh = gen_mu * 1000.0  # Convert MU to MWh (1 MU = 1,000 MWh)
                                    year = str(gen_record.get("year", "Unknown"))

                                    if gen_mwh > 0:
                                        # CUF = (Annual Generation MWh) / (Capacity MW * 8760 hours) as decimal
                                        cuf_decimal = gen_mwh / (plant_capacity * 8760)
                                        cuf_data.append({
                                            "value": round(cuf_decimal, 4),  # Decimal format (e.g., 0.25 instead of 25%)
                                            "year": year
                                        })

                            if cuf_data:
                                plant_data["cuf"] = cuf_data
                                # Add source tracking for calculated CUF
                                plant_data["cuf_sources"] = [
                                    "Calculated from gross_power_generation data using formula: CUF = (Annual Generation MU × 1000) / (Capacity MW × 8760)"
                                ]
                                print(f"[Session {session_id}] ✅ Calculated CUF from generation data (decimal format, converted from MU) with source tracking")

                        # Add fallback gross_power_generation if missing (5 years: 2020-2024)
                        if not plant_data.get("gross_power_generation") and plant_capacity > 0:
                            # Estimate generation based on plant type and capacity
                            typical_cuf = {
                                "solar": 0.20,    # 20% CUF for solar
                                "wind": 0.25,     # 25% CUF for wind
                                "thermal": 0.60,  # 60% CUF for thermal
                                "biomass": 0.70,  # 70% CUF for biomass
                                "hydro": 0.40     # 40% CUF for hydro
                            }.get(plant_type, 0.50)  # Default 50% CUF

                            estimated_annual_generation_mwh = plant_capacity * 8760 * typical_cuf  # MWh
                            # Convert MWh to MU (Million Units): 1 MU = 1,000 MWh
                            estimated_annual_generation_mu = estimated_annual_generation_mwh / 1000.0  # MU
                            plant_data["gross_power_generation"] = [
                                {"value": round(estimated_annual_generation_mu, 3), "year": str(year)}
                                for year in [2020, 2021, 2022, 2023, 2024]
                            ]
                            # Add source tracking for fallback generation data
                            plant_data["gross_power_generation_sources"] = [
                                f"Calculated using industry standards for {plant_type} plants (Capacity: {plant_capacity} MW × 8760 hours × {typical_cuf*100}% CUF ÷ 1000 = {estimated_annual_generation_mu:.3f} MU annually)"
                            ]
                            print(f"[Session {session_id}] ✅ Added fallback generation data (5 years, {typical_cuf*100}% CUF, in MU) with source tracking")

                    except Exception as e:
                        print(f"[Session {session_id}] ⚠️ Plant fallback calculations failed for Plant {plant_id}: {str(e)}")
                    
                    # Filter plant data to match template - remove extra fields
                    plant_data = filter_plant_data_to_template(plant_data, session_id)
                    
                    # Store each plant in the same folder with the plant name
                    # Ensure plant_id is included in the filename to prevent overwriting
                    plant_id_str = str(plant_data.get('plant_id', ''))
                    print(f"[Session {session_id}] 🔍 DEBUG: Calling store_plant_data with plant_id: {plant_id_str}")
                    print(f"[Session {session_id}] 🏭 STORING PLANT {plant_id_str}: {plant_data.get('name', 'Unknown')}")
                    
                    # Force a unique filename for each plant
                    forced_filename = f"plant_level_{plant_id_str}.json"
                    print(f"[Session {session_id}] 🔍 DEBUG: Forcing filename: {forced_filename}")
                    
                    # Create a modified version of the plant data with a forced filename
                    # This is a workaround since we can't use the override_filename parameter
                    plant_data_copy = plant_data.copy()
                    plant_data_copy["_filename_override"] = forced_filename
                    
                    # Add a timestamp to help debug
                    import time
                    timestamp = int(time.time())
                    print(f"[Session {session_id}] 🕒 TIMESTAMP BEFORE STORE: {timestamp}")
                    
                    # Get UID context for hierarchical storage
                    org_uid = state.get("org_uid", "")
                    plant_uid = state.get("plant_uid", "")

                    # Use original plant name for database lookup, not the modified name with Plant_ID
                    messages = state.get("messages", [])
                    if messages:
                        original_plant_name = get_research_topic(messages)
                    else:
                        original_plant_name = plant_name_for_s3  # Fallback

                    print(f"[Session {session_id}] 🔍 Using original plant name for database lookup: {original_plant_name}")

                    plant_s3_url = store_plant_data(
                        plant_data_copy,
                        original_plant_name,  # Use original plant name for database lookup
                        session_id,
                        org_uid,
                        plant_uid
                    )
                    
                    if plant_s3_url:
                        print(f"[Session {session_id}] ✅ Plant {plant_id} JSON saved to S3: {plant_s3_url}")
                        if "plants" not in current_s3_urls:
                            current_s3_urls["plants"] = {}
                        current_s3_urls["plants"][str(plant_id)] = plant_s3_url
                        
                        if "plants" not in current_storage_status:
                            current_storage_status["plants"] = {}
                        current_storage_status["plants"][str(plant_id)] = True
                    else:
                        print(f"[Session {session_id}] ❌ Failed to save Plant {plant_id} JSON to S3")
                        current_errors.append(f"Failed to save Plant {plant_id} JSON to S3")
            else:
                raise Exception("Could not extract valid JSON from plant response")
            
        except Exception as e:
            import traceback
            print(f"Error in plant-level generation: {str(e)}")
            print(traceback.format_exc())
            
            # Fallback plant-level response
            plant_name = research_topic.strip()
            final_content = f"""PLANT-LEVEL INFORMATION:
{{
  "sk": "plant#Unknown#1",
  "pk": "default null",
  "annual_operational_hours": 8760,
  "auxiliary_power_consumed": [],
  "closure_year": "",
  "commencement_date": "",
  "cuf": [],
  "gross_power_generation": [],
  "grid_connectivity_maps": [],
  "installed_bess_capacity": "",
  "installed_bess_capacity_unit": "MWh",
  "installed_biomass_capacity": "",
  "installed_biomass_capacity_unit": "MW",
  "installed_solar_capacity": "",
  "installed_solar_capacity_unit": "MW",
  "installed_wind_capacity": "",
  "installed_wind_capacity_unit": "MW",
  "latitude": "",
  "longitude": "",
  "mandatory_closure": "",
  "name": "{plant_name}",
  "plant_address": "",
  "plant_id": 1,
  "plant_images": [],
  "plant_lifetime": 25,
  "plant_type": "Unknown",
  "potential_reference": {{"lat": null, "long": null}},
  "ppa_details": [],
  "remaining_useful_life": "",
  "total_installed_capacity": "",
  "total_installed_capacity_unit": "MW"
}}"""
            
            current_s3_urls = state.get("s3_json_urls", {})
            current_storage_status = state.get("json_storage_complete", {})
            current_errors = state.get("json_storage_errors", [])
            current_errors.append(f"Plant-level extraction failed: {str(e)}")
            
            print("Using fallback plant-level information")

        # Filter sources that were actually used in the response
        unique_sources = []
        for source in state.get("sources_gathered", []):
            if isinstance(source, dict):
                if ("short_url" in source and source["short_url"] in final_content) or \
                   ("value" in source and source["value"] in final_content):
                    unique_sources.append(source)
            elif hasattr(source, "short_url") and source.short_url in final_content:
                unique_sources.append(source)
            elif hasattr(source, "value") and source.value in final_content:
                unique_sources.append(source)

        # Return plant-level results with S3 updates
        return {
            "messages": [AIMessage(content=final_content)],
            "sources_gathered": unique_sources,
            "s3_json_urls": current_s3_urls,
            "json_storage_complete": current_storage_status,
            "json_storage_errors": current_errors,
        }
    
    else:
        # No other phases needed - only organization and plant level
        return {
            "messages": [AIMessage(content="Processing complete. Organization and plant level data extracted.")],
            "sources_gathered": state.get("sources_gathered", []),
            "s3_json_urls": state.get("s3_json_urls", {}),
            "json_storage_complete": state.get("json_storage_complete", {}),
            "json_storage_errors": state.get("json_storage_errors", []),
        }


# Define a clear state transition node between organization and plant level
def clear_state_for_plant_level(state: OverallState) -> OverallState:
    """Clear the state for plant-level research after organization-level is complete."""
    session_id = state.get("session_id", "unknown")
    research_topic = state.get("research_topic", "")

    print(f"[Session {session_id}] 🔄 CLEARING STATE FOR PLANT LEVEL")
    print(f"[Session {session_id}] Organization phase complete, starting plant phase")
    print(f"[Session {session_id}] 🔍 DEBUG: State keys: {list(state.keys())}")
    print(f"[Session {session_id}] 🔍 DEBUG: research_topic from state: '{research_topic}'")
    print(f"[Session {session_id}] 🔍 DEBUG: Messages count: {len(state.get('messages', []))}")

    # If research_topic is missing, try to extract from messages as fallback
    if not research_topic or not research_topic.strip():
        print(f"[Session {session_id}] ⚠️ Research topic missing from state, extracting from messages...")
        from agent.utils import get_research_topic
        research_topic = get_research_topic(state.get("messages", []))
        print(f"[Session {session_id}] 🔍 DEBUG: Extracted research topic from messages: '{research_topic}'")

    print(f"[Session {session_id}] Validating research topic for plant phase: '{research_topic}'")
    
    # Validate that we have a valid research topic before proceeding to plant phase
    if not research_topic or not research_topic.strip():
        print(f"[Session {session_id}] ❌ ERROR: Cannot proceed to plant phase without research topic!")
        print(f"[Session {session_id}] Terminating workflow due to missing research topic")
        
        # Return terminal state instead of proceeding
        return {
            "messages": state.get("messages", []) + [AIMessage(content="ERROR: Cannot proceed with research - no topic provided")],
            "session_id": session_id,
            "search_phase": 999,  # Invalid phase to stop workflow
            "phase_complete": True,
            "research_complete": True,
            "error": "No research topic provided"
        }
    
    # Preserve essential data and clear research-specific data for new phase
    return {
        "messages": state.get("messages", []),
        "session_id": session_id,
        "research_topic": research_topic,  # Preserve research topic
        "search_phase": 2,  # Switch to plant-level
        "research_loop_count": 0,  # Reset for plant phase
        "web_research_result": [],  # Clear for new phase
        "search_query": [],  # Clear for new phase
        "sources_gathered": [],  # Clear for new phase
        "continue_research": False,  # Reset
        "phase_complete": False,  # Reset
        "initial_search_query_count": state.get("initial_search_query_count", 5),
        "max_research_loops": state.get("max_research_loops", 3),
        "reasoning_model": state.get("reasoning_model", ""),
        # Preserve S3 and session data
        "plant_name_for_s3": state.get("plant_name_for_s3", ""),
        "s3_json_urls": state.get("s3_json_urls", {}),
        "json_storage_complete": state.get("json_storage_complete", {}),
        "json_storage_errors": state.get("json_storage_errors", []),
        # Preserve image extraction results
        "image_extraction_complete": state.get("image_extraction_complete", False),
        "s3_image_urls": state.get("s3_image_urls", []),
        "image_extraction_error": state.get("image_extraction_error", ""),
    }


# Create our Agent Graph
builder = StateGraph(OverallState, config_schema=Configuration)

# Add session initialization node
builder.add_node("initialize_session", initialize_session)

# Add registry nodes for plant discovery and entity extraction
builder.add_node("check_plant_registry", check_plant_registry)
builder.add_node("quick_org_discovery", quick_org_discovery_node)
builder.add_node("generate_uid", generate_uid_node)
builder.add_node("populate_database_async", populate_database_async_node)
builder.add_node("entity_extraction_trigger", entity_extraction_trigger_node)

# Add AGI integration node
builder.add_node("save_agi_org_uid_directly", save_agi_org_uid_directly)

# Add parallel image extraction node
builder.add_node("extract_images_parallel", extract_images_parallel)

# Define organization-level research nodes
builder.add_node("org_generate_query", lambda state, config: generate_query({**state, "search_phase": 1}, config))
builder.add_node("org_web_research", web_research)
builder.add_node("org_reflection", reflection)
builder.add_node("org_finalize_answer", lambda state, config: finalize_answer_parallel({**state, "search_phase": 1}, config))

# Add state transition node
builder.add_node("clear_state_for_plant_level", clear_state_for_plant_level)

# Define plant-level research nodes  
builder.add_node("plant_generate_query", lambda state, config: generate_query({**state, "search_phase": 2}, config))
builder.add_node("plant_web_research", web_research)
builder.add_node("plant_reflection", reflection)

def plant_finalize_answer_debug(state, config):
    session_id = state.get("session_id", "unknown")
    plant_processing_active = state.get("plant_processing_active", False)
    current_plant_name = state.get("current_plant_name", "Unknown")

    print(f"[Session {session_id}] 🔍 DEBUG: plant_finalize_answer called")
    print(f"[Session {session_id}] Plant processing active: {plant_processing_active}")
    print(f"[Session {session_id}] Current plant: {current_plant_name}")

    # CRITICAL: For sequential processing, trigger image extraction for current plant
    print(f"[Session {session_id}] 🔍 DEBUG: Checking image extraction conditions")
    print(f"[Session {session_id}] 🔍 DEBUG: plant_processing_active = {plant_processing_active}")
    print(f"[Session {session_id}] 🔍 DEBUG: current_plant_name = '{current_plant_name}'")

    if plant_processing_active and current_plant_name and current_plant_name != "Unknown":
        print(f"[Session {session_id}] 🖼️ STARTING IMAGE EXTRACTION for plant: {current_plant_name}")
        try:
            # Extract images for current plant
            print(f"[Session {session_id}] 🖼️ Calling extract_and_upload_images...")
            s3_image_urls = extract_and_upload_images(current_plant_name, session_id)
            print(f"[Session {session_id}] 🖼️ Image extraction returned: {s3_image_urls}")

            if s3_image_urls:
                print(f"[Session {session_id}] ✅ Image extraction successful: {len(s3_image_urls)} images")
                # Add image URLs to state for plant JSON processing
                state = {
                    **state,
                    "s3_image_urls": s3_image_urls,
                    "image_extraction_complete": True,
                    "image_extraction_error": ""
                }
            else:
                print(f"[Session {session_id}] ⚠️ No images found for plant: {current_plant_name}")
                state = {
                    **state,
                    "s3_image_urls": [],
                    "image_extraction_complete": True,
                    "image_extraction_error": "No images found"
                }
        except Exception as e:
            print(f"[Session {session_id}] ❌ Image extraction failed: {str(e)}")
            import traceback
            print(f"[Session {session_id}] ❌ Image extraction traceback: {traceback.format_exc()}")
            state = {
                **state,
                "s3_image_urls": [],
                "image_extraction_complete": True,
                "image_extraction_error": str(e)
            }
    else:
        print(f"[Session {session_id}] ⚠️ Skipping image extraction - conditions not met")

    result = finalize_answer_parallel({**state, "search_phase": 2}, config)

    # For entity-level extraction, don't save to DynamoDB here (will be done at the end)
    if plant_processing_active:
        print(f"[Session {session_id}] 🔄 Entity-level processing - plant completed, continuing to next")
        # Don't save to DynamoDB yet - will be done after all plants are processed
        print(f"[Session {session_id}] ✅ plant_finalize_answer completed (entity-level)")
        return result

    # For single plant processing, save to DynamoDB
    try:
        print(f"[Session {session_id}] 💾 Saving details to DynamoDB...")

        # Get DynamoDB manager
        dynamodb_manager = get_dynamodb_manager()

        if dynamodb_manager.is_available():
            # Extract organization and plant information from state (discovered data)
            org_uid = state.get("org_uid", "unknown")
            research_topic = get_research_topic(state.get("messages", []))

            # Use discovered organization data from state (PRIORITY)
            organization_name = state.get("discovered_org_name", "Unknown Organization")
            discovered_plants = state.get("discovered_plants", [])

            # Extract plant names from discovered plants
            if discovered_plants:
                plant_names = [plant.get("name", plant.get("plant_name", "Unknown Plant"))
                              for plant in discovered_plants]
                # Remove duplicates and empty names
                plant_names = list(set([name for name in plant_names if name and name != "Unknown Plant"]))
            else:
                plant_names = [research_topic]  # Fallback to input plant

            print(f"[Session {session_id}] 🔍 Organization data for DynamoDB:")
            print(f"   Organization: {organization_name}")
            print(f"   Org UID: {org_uid}")
            print(f"   Plants: {len(plant_names)} plants")
            print(f"   Plant Names: {', '.join(plant_names)}")

            # Fallback: Try to extract from result content if state data is missing
            if organization_name == "Unknown Organization":
                messages = result.get("messages", [])
                if messages:
                    content = messages[0].content if hasattr(messages[0], 'content') else str(messages[0])
                    org_match = re.search(r'"org_name":\s*"([^"]+)"', content)
                    if org_match:
                        organization_name = org_match.group(1)
                        print(f"[Session {session_id}] 🔍 Extracted org name from content: {organization_name}")

            # Save organization details (only 3 required fields)
            success = dynamodb_manager.save_organization_details(
                organization_name=organization_name,
                org_uid=org_uid,
                plant_names=plant_names
            )

            if success:
                print(f"[Session {session_id}] ✅ Organization details saved to DynamoDB")
            else:
                print(f"[Session {session_id}] ⚠️ Failed to save organization details to DynamoDB")
        else:
            print(f"[Session {session_id}] ⚠️ DynamoDB not available - skipping save")

    except Exception as e:
        print(f"[Session {session_id}] ❌ Error saving to DynamoDB: {e}")

    print(f"[Session {session_id}] ✅ plant_finalize_answer completed")
    return result

builder.add_node("plant_finalize_answer", plant_finalize_answer_debug)

# Add routing function for AGI vs normal flow
def route_after_initialization(state: OverallState) -> str:
    """
    Route after session initialization based on AGI context

    Args:
        state: Current graph state

    Returns:
        Next node name
    """
    session_id = state.get("session_id", "unknown")
    entity_id = state.get("entity_id")

    print(f"[Session {session_id}] 🔍 DEBUG: route_after_initialization called")
    print(f"[Session {session_id}] 🔍 DEBUG: State keys: {list(state.keys())}")
    print(f"[Session {session_id}] 🔍 DEBUG: entity_id value: {entity_id}")
    print(f"[Session {session_id}] 🔍 DEBUG: entity_id type: {type(entity_id)}")

    if entity_id:
        print(f"[Session {session_id}] 🔧 AGI context detected - routing to AGI UID handler")
        print(f"[Session {session_id}] Entity ID: {entity_id}")
        return "save_agi_org_uid_directly"
    else:
        print(f"[Session {session_id}] 🔧 Normal flow - routing to plant registry check")
        return "check_plant_registry"

# Add the graph edges for enhanced processing with registry and entity extraction
# Start with session initialization
builder.add_edge(START, "initialize_session")

# After initialization, route based on AGI context
builder.add_conditional_edges(
    "initialize_session",
    route_after_initialization,
    {
        "save_agi_org_uid_directly": "save_agi_org_uid_directly",  # AGI flow
        "check_plant_registry": "check_plant_registry"  # Normal flow
    }
)

# AGI UID routing: discovery or direct processing
def route_after_agi_uid_save(state: OverallState) -> str:
    """
    Route after AGI UID saving based on plant existence

    Args:
        state: Current graph state

    Returns:
        Next node name
    """
    session_id = state.get("session_id", "unknown")
    plant_exists = state.get("plant_exists_in_db", False)

    if plant_exists:
        print(f"[Session {session_id}] 🔧 Plant exists in DB - skipping discovery")
        return "spawn_parallel_processing_with_uid"
    else:
        print(f"[Session {session_id}] 🔧 Plant not in DB - need discovery")
        return "quick_org_discovery"

builder.add_conditional_edges(
    "save_agi_org_uid_directly",
    route_after_agi_uid_save,
    {
        "quick_org_discovery": "quick_org_discovery",  # New plants, need discovery
        "spawn_parallel_processing_with_uid": "spawn_parallel_processing_with_uid"  # Existing plants, direct processing
    }
)

# Registry routing: discovery or UID generation
builder.add_conditional_edges(
    "check_plant_registry",
    route_after_registry_check,
    {
        "quick_org_discovery": "quick_org_discovery",  # New plants, need discovery
        "generate_uid": "generate_uid"  # Existing plants, skip to UID
    }
)

# After discovery, route based on AGI context
def route_after_quick_discovery(state: OverallState) -> str:
    """
    Route after quick organization discovery based on AGI context

    Args:
        state: Current graph state

    Returns:
        Next node name
    """
    session_id = state.get("session_id", "unknown")
    entity_id = state.get("entity_id")

    if entity_id:
        print(f"[Session {session_id}] 🔧 AGI context detected after discovery - skipping UID generation")
        print(f"[Session {session_id}]    Using AGI Entity ID: {entity_id}")
        return "populate_database_async"  # Skip UID generation, go directly to database
    else:
        print(f"[Session {session_id}] 🔧 Normal flow after discovery - generating UIDs")
        return "generate_uid"  # Normal flow, generate UIDs

builder.add_conditional_edges(
    "quick_org_discovery",
    route_after_quick_discovery,
    {
        "generate_uid": "generate_uid",  # Normal flow
        "populate_database_async": "populate_database_async"  # AGI flow, skip UID generation
    }
)

# After UID generation, populate database
builder.add_conditional_edges(
    "generate_uid",
    route_after_uid_generation,
    {
        "populate_database_async": "populate_database_async"
    }
)

# After database population, route to entity extraction or single plant processing
builder.add_conditional_edges(
    "populate_database_async",
    route_after_database_population,
    {
        "entity_extraction_trigger": "entity_extraction_trigger",  # Multi-plant: trigger entity extraction
        "spawn_parallel_processing_with_uid": "spawn_parallel_processing_with_uid"  # Single plant: normal processing
    }
)

# Entity extraction trigger routes directly to END (simple for-loop approach)
builder.add_edge("entity_extraction_trigger", END)

# Add a wrapper node that triggers parallel processing
def spawn_parallel_processing_with_uid(state: OverallState):
    """Node wrapper that triggers parallel processing with UID context"""
    session_id = state.get("session_id", "unknown")
    org_uid = state.get("org_uid", "")
    plant_uid = state.get("plant_uid", "")

    print(f"[Session {session_id}] 🚀 Starting parallel processing with UID context")
    print(f"   Org UID: {org_uid}")
    print(f"   Plant UID: {plant_uid}")

    # Add UID context to state for S3 storage
    return {
        "org_uid": org_uid,
        "plant_uid": plant_uid,
        "uid_context_ready": True
    }

builder.add_node("spawn_parallel_processing_with_uid", spawn_parallel_processing_with_uid)

# Add sequential plant processing node
def process_next_plant(state: OverallState):
    """Process plants sequentially for entity-level extraction"""
    session_id = state.get("session_id", "unknown")
    discovered_plants = state.get("discovered_plants", [])
    current_plant_index = state.get("current_plant_index", 0)
    processed_plants = state.get("processed_plants", [])
    org_uid = state.get("org_uid", "")

    print(f"[Session {session_id}] 🔄 Sequential plant processing")
    print(f"   Total plants: {len(discovered_plants)}")
    print(f"   Current index: {current_plant_index}")
    print(f"   Processed: {len(processed_plants)}")

    # Check if we've processed all plants
    if current_plant_index >= len(discovered_plants):
        print(f"[Session {session_id}] ✅ All plants processed - completing entity extraction")
        return {
            **state,
            "all_plants_processed": True,
            "entity_extraction_complete": True
        }

    # Get current plant to process
    current_plant = discovered_plants[current_plant_index]
    plant_name = current_plant.get("name", f"Plant_{current_plant_index}")

    print(f"[Session {session_id}] 🌱 Processing plant {current_plant_index + 1}/{len(discovered_plants)}: {plant_name}")

    # Check if this plant is already being processed (avoid infinite loop)
    current_plant_name = state.get("current_plant_name", "")
    if current_plant_name == plant_name:
        print(f"[Session {session_id}] ⚠️ Plant {plant_name} is already being processed - skipping setup")
        # This plant is already set up, just continue with the research flow
        return {
            **state,
            "current_plant_name": plant_name,
            "current_plant_data": current_plant
        }

    # Get or generate plant UID - handle missing org_uid
    from agent.database_manager import get_database_manager
    db_manager = get_database_manager()

    # If no org_uid, generate a temporary one for this session
    if not org_uid:
        import uuid
        org_uid = f"temp_org_{str(uuid.uuid4())[:8]}"
        print(f"[Session {session_id}] ⚠️ No org_uid available, using temporary: {org_uid}")

    plant_uid = db_manager.get_plant_uid_from_database(plant_name, org_uid)
    if not plant_uid:
        # Generate plant UID without storing in database if org_uid is temporary
        if org_uid.startswith("temp_org_"):
            plant_uid = str(uuid.uuid4())
            print(f"[Session {session_id}] 🔧 Generated temporary plant UID: {plant_uid}")
        else:
            plant_uid = db_manager.generate_and_store_plant_uid(plant_name, org_uid, "Unknown Organization", "Unknown")

    # Set up state for this plant's processing
    from langchain_core.messages import HumanMessage

    return {
        **state,
        "current_plant_name": plant_name,
        "current_plant_data": current_plant,
        "plant_uid": plant_uid,
        "current_plant_index": current_plant_index,
        "messages": [HumanMessage(content=plant_name)],  # Set plant as research topic with proper message type
        "search_phase": 2,  # Plant-level processing
        "web_research_result": [],  # Reset research results for this plant
        "sources_gathered": [],  # Reset sources for this plant
        "research_loop_count": 0,  # Reset research loop
        "continue_research": True,  # Start research for this plant
        "phase_complete": False,  # Reset phase completion
        "plant_processing_active": True
    }

builder.add_node("process_next_plant", process_next_plant)

# Route from process_next_plant
def route_from_plant_processor(state: OverallState) -> str:
    """Route from plant processor based on completion status"""
    all_plants_processed = state.get("all_plants_processed", False)

    if all_plants_processed:
        return "finalize_entity_extraction"
    else:
        return "plant_generate_query"  # Start plant-level research

builder.add_conditional_edges(
    "process_next_plant",
    route_from_plant_processor,
    {
        "plant_generate_query": "plant_generate_query",  # Process current plant
        "finalize_entity_extraction": "finalize_entity_extraction"  # All plants done
    }
)

# Add node to advance to next plant
def advance_to_next_plant(state: OverallState):
    """Advance to the next plant in the sequence"""
    session_id = state.get("session_id", "unknown")
    current_plant_index = state.get("current_plant_index", 0)
    current_plant_name = state.get("current_plant_name", "Unknown")
    processed_plants = state.get("processed_plants", [])

    print(f"[Session {session_id}] ✅ Completed processing plant: {current_plant_name}")

    # Add current plant to processed list
    processed_plants.append({
        "name": current_plant_name,
        "index": current_plant_index,
        "status": "completed"
    })

    # Advance to next plant
    next_index = current_plant_index + 1

    print(f"[Session {session_id}] ➡️ Advancing to plant index: {next_index}")

    return {
        **state,
        "current_plant_index": next_index,
        "processed_plants": processed_plants,
        "plant_processing_active": True,  # Keep TRUE for entity-level processing
        "web_research_result": [],  # Clear research results
        "sources_gathered": [],  # Clear sources
        "research_loop_count": 0,  # Reset research loop
        "continue_research": True,  # Reset research flag
        "phase_complete": False  # Reset phase completion
    }

builder.add_node("advance_to_next_plant", advance_to_next_plant)

# Add finalization node for entity extraction
def finalize_entity_extraction(state: OverallState):
    """Finalize entity extraction after all plants are processed"""
    session_id = state.get("session_id", "unknown")
    processed_plants = state.get("processed_plants", [])
    discovered_plants = state.get("discovered_plants", [])

    print(f"[Session {session_id}] 🎉 Entity extraction completed!")
    print(f"   Total plants discovered: {len(discovered_plants)}")
    print(f"   Total plants processed: {len(processed_plants)}")

    # Save to DynamoDB
    try:
        dynamodb_manager = get_dynamodb_manager()
        if dynamodb_manager.is_available():
            org_uid = state.get("org_uid", "unknown")
            org_name = "Unknown Organization"  # Will be extracted from data
            plant_names = [plant.get("name", f"Plant_{i}") for i, plant in enumerate(discovered_plants)]

            success = dynamodb_manager.save_organization_details(
                organization_name=org_name,
                org_uid=org_uid,
                plant_names=plant_names
            )

            if success:
                print(f"[Session {session_id}] ✅ Entity extraction results saved to DynamoDB")
            else:
                print(f"[Session {session_id}] ⚠️ Failed to save entity extraction results to DynamoDB")
        else:
            print(f"[Session {session_id}] ⚠️ DynamoDB not available")
    except Exception as e:
        print(f"[Session {session_id}] ❌ Error saving entity extraction results: {e}")

    return {
        **state,
        "entity_extraction_complete": True,
        "final_plant_count": len(discovered_plants),
        "processed_plant_count": len(processed_plants)
    }

builder.add_node("finalize_entity_extraction", finalize_entity_extraction)
builder.add_edge("finalize_entity_extraction", END)

# From the wrapper, start both image extraction and organization research
builder.add_edge("spawn_parallel_processing_with_uid", "extract_images_parallel")
builder.add_edge("spawn_parallel_processing_with_uid", "org_generate_query")

# Organization-level flow
builder.add_edge("org_generate_query", "org_web_research")
builder.add_edge("org_web_research", "org_reflection")

def should_continue_org_research(state):
    continue_research = state.get("continue_research", False)
    research_loop_count = state.get("research_loop_count", 0)
    max_research_loops = state.get("max_research_loops", 3)
    
    if continue_research and research_loop_count < max_research_loops:
        return "org_generate_query"
    else:
        return "org_finalize_answer"

builder.add_conditional_edges("org_reflection", should_continue_org_research)
builder.add_edge("org_finalize_answer", "clear_state_for_plant_level")

# Plant-level flow
builder.add_edge("clear_state_for_plant_level", "plant_generate_query") 
builder.add_edge("plant_generate_query", "plant_web_research")
builder.add_edge("plant_web_research", "plant_reflection")

def should_continue_plant_research(state):
    continue_research = state.get("continue_research", False)
    research_loop_count = state.get("research_loop_count", 0)
    max_research_loops = state.get("max_research_loops", 3)
    
    if continue_research and research_loop_count < max_research_loops:
        return "plant_generate_query"
    else:
        return "plant_finalize_answer"

builder.add_conditional_edges("plant_reflection", should_continue_plant_research)
# Route from plant_finalize_answer based on processing mode
def route_after_plant_finalize(state: OverallState) -> str:
    """Route after plant finalization based on processing mode"""
    plant_processing_active = state.get("plant_processing_active", False)

    if plant_processing_active:
        return "advance_to_next_plant"  # Entity-level: continue to next plant
    else:
        return "END"  # Single plant: end processing

builder.add_conditional_edges(
    "plant_finalize_answer",
    route_after_plant_finalize,
    {
        "advance_to_next_plant": "advance_to_next_plant",  # Continue entity processing
        "END": END  # End single plant processing
    }
)

# Connect advance_to_next_plant back to process_next_plant
builder.add_edge("advance_to_next_plant", "process_next_plant")

# Create the compiled graph with increased recursion limit for sequential processing
# Configure for sequential plant processing (4 plants × ~15 steps each = ~60 steps)
graph = builder.compile()