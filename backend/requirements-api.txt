# FastAPI Dependencies for Power Plant Extraction API
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
python-multipart==0.0.6

# WebSocket support
websockets==12.0

# CORS middleware
python-cors==1.7.0

# Background tasks and async support
asyncio-mqtt==0.13.0

# JSON handling
orjson==3.9.10

# Existing project dependencies (ensure compatibility)
# These should already be installed from the main requirements.txt
# langchain-core
# langgraph
# sqlalchemy
# boto3
# python-dotenv
