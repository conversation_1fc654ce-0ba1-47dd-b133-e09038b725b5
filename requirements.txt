absl-py==2.3.0
# Editable Git install with no remote (agent==0.0.1)
-e /Users/<USER>/Documents/clem_enablement/backend
annotated-types==0.7.0
anyio==4.9.0
attrs==25.3.0
beautifulsoup4==4.13.4
blockbuster==1.5.24
boto3==1.38.42
botocore==1.38.42
bs4==0.0.2
cachetools==5.5.2
certifi==2025.6.15
cffi==1.17.1
charset-normalizer==3.4.2
click==8.2.1
cloudpickle==3.1.1
contourpy==1.3.2
cryptography==44.0.3
cycler==0.12.1
dotenv==0.9.9
easyocr==1.7.2
fastapi==0.115.14
filelock==3.18.0
filetype==1.2.0
flatbuffers==25.2.10
fonttools==4.58.4
forbiddenfruit==0.1.4
fsspec==2025.5.1
google-ai-generativelanguage==0.6.18
google-api-core==2.25.1
google-auth==2.40.3
google-genai==1.22.0
googleapis-common-protos==1.70.0
grpcio==1.73.1
grpcio-status==1.62.3
h11==0.16.0
httpcore==1.0.9
httpx==0.28.1
idna==3.10
imageio==2.37.0
jax==0.6.2
jaxlib==0.6.2
Jinja2==3.1.6
jmespath==1.0.1
jsonpatch==1.33
jsonpointer==3.0.0
jsonschema_rs==0.29.1
kiwisolver==1.4.8
langchain==0.3.26
langchain-core==0.3.66
langchain-google-genai==2.1.5
langchain-text-splitters==0.3.8
langgraph==0.5.0
langgraph-api==0.2.71
langgraph-checkpoint==2.1.0
langgraph-cli==0.3.3
langgraph-prebuilt==0.5.0
langgraph-runtime-inmem==0.3.3
langgraph-sdk==0.1.72
langsmith==0.4.3
lazy_loader==0.4
MarkupSafe==3.0.2
matplotlib==3.10.3
mediapipe==0.10.21
ml_dtypes==0.5.1
mpmath==1.3.0
networkx==3.5
ninja==********
numpy==1.26.4
opencv-contrib-python==*********
opencv-python==*********
opencv-python-headless==*********
opt_einsum==3.4.0
orjson==3.10.18
ormsgpack==1.10.0
packaging==24.2
pillow==11.2.1
proto-plus==1.26.1
protobuf==4.25.8
pyasn1==0.6.1
pyasn1_modules==0.4.2
pyclipper==1.3.0.post6
pycparser==2.22
pydantic==2.11.7
pydantic_core==2.33.2
PyJWT==2.10.1
pyparsing==3.2.3
python-bidi==0.6.6
python-dateutil==2.9.0.post0
python-dotenv==1.1.1
PyYAML==6.0.2
requests==2.32.4
requests-toolbelt==1.0.0
rsa==4.9.1
s3transfer==0.13.0
scikit-image==0.25.2
scipy==1.16.0
sentencepiece==0.2.0
setuptools==78.1.1
shapely==2.1.1
six==1.17.0
sniffio==1.3.1
sounddevice==0.5.2
soupsieve==2.7
SQLAlchemy==2.0.41
sse-starlette==2.1.3
starlette==0.46.2
structlog==25.4.0
sympy==1.14.0
tenacity==8.5.0
tifffile==2025.6.11
torch==2.7.1
torchvision==0.22.1
truststore==0.10.1
typing-inspection==0.4.1
typing_extensions==4.14.0
urllib3==2.5.0
uvicorn==0.34.3
watchfiles==1.1.0
websockets==15.0.1
wheel==0.45.1
xxhash==3.5.0
zstandard==0.23.0
