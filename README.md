# Power Plant Intelligence System

This project is an advanced **Power Plant Data Intelligence System** built with React frontend and LangGraph-powered backend. The system specializes in **extracting comprehensive power plant information** from web research, generating structured JSON data that complies with specific templates for organization-level, plant-level, and unit-level information. It's designed for energy sector professionals who need detailed, accurate power plant data for analysis and decision-making.

![Gemini Fullstack LangGraph](./app.png)

## Features

### 🏭 **Power Plant Data Extraction**
- **Multi-Level Data Collection**: Organization, Plant, and Unit level information
- **Multiple Plant Support**: Automatically detects and creates separate JSONs for multiple plants
- **Plant Type Intelligence**: Capacity fields automatically populated based on plant technology (solar, wind, thermal, etc.)
- **Comprehensive Data Coverage**: Technical specifications, financial data, operational metrics, and regulatory information

### 🔍 **Advanced Research Capabilities**
- **Dynamic Query Generation**: Specialized search queries for power plant data using Google Gemini models
- **Iterative Search Refinement**: Identifies knowledge gaps and generates follow-up queries
- **Source Verification**: Tracks and cites all information sources
- **Image Extraction**: Automatically extracts and processes power plant images

### 📊 **Smart Data Processing**
- **Fallback Calculations**: Automatic calculation of missing data (CUF, auxiliary power consumption) using industry standards
- **Template Compliance**: Ensures all generated JSON strictly follows predefined templates
- **Data Validation**: Built-in validation for data quality and completeness
- **S3 Integration**: Automatic storage of structured data to AWS S3

### 🎯 **Specialized Features**
- **PPA Analysis**: Detailed Power Purchase Agreement extraction with nested contract details
- **Grid Connectivity**: Comprehensive transmission and substation information
- **Capacity Utilization**: Calculation of CUF using formula: (Annual Generation)/(Capacity × 8760)
- **Auxiliary Power Standards**: Technology-specific auxiliary power consumption estimates
- **Coordinate Extraction**: GPS coordinates from plant addresses or direct mention

### � **Technical Infrastructure**
- **React Frontend**: Modern, responsive interface for power plant research
- **LangGraph Backend**: Advanced agent workflow for complex data extraction
- **Hot Reloading**: Development environment with real-time updates
- **Docker Support**: Production-ready containerization

## Project Structure

```
clem_enablement/
├── frontend/                 # React application with Vite
│   ├── src/                 # Frontend source code
│   └── public/              # Static assets
├── backend/                 # LangGraph/FastAPI application
│   ├── src/agent/           # Power plant research agent
│   │   ├── graph.py         # Main agent workflow
│   │   ├── prompts.py       # Specialized power plant prompts
│   │   ├── fallback_calculations.py  # Industry standard calculations
│   │   ├── json_s3_storage.py       # S3 integration
│   │   └── tools_and_schemas.py     # Data schemas and validation
│   └── .env                 # Environment configuration
├── org_level.json           # Organization-level data template
├── plant_level.json         # Plant-level data template
├── unit_level.json          # Unit-level data template
└── README.md               # This file
```

## Getting Started: Development and Local Testing

Follow these steps to get the application running locally for development and testing.

**1. Prerequisites:**

-   Node.js and npm (or yarn/pnpm)
-   Python 3.8+
-   **AWS S3 Access** (for storing generated JSON files)
-   **Google Search API Key** (for web research)
-   **`GEMINI_API_KEY`**: The backend agent requires a Google Gemini API key.
    1.  Navigate to the `backend/` directory.
    2.  Create a file named `.env` by copying the `backend/.env.example` file.
    3.  Open the `.env` file and add your API keys:
        ```
        GEMINI_API_KEY="YOUR_ACTUAL_GEMINI_API_KEY"
        GOOGLE_CUSTOM_SEARCH_API_KEY="YOUR_SEARCH_API_KEY"
        GOOGLE_CUSTOM_SEARCH_ENGINE_ID="YOUR_SEARCH_ENGINE_ID"
        AWS_ACCESS_KEY_ID="YOUR_AWS_ACCESS_KEY"
        AWS_SECRET_ACCESS_KEY="YOUR_AWS_SECRET_KEY"
        S3_BUCKET_NAME="your-s3-bucket-name"
        ```

**2. Install Dependencies:**

**Backend:**

```bash
cd backend
pip install .
```

**Frontend:**

```bash
cd frontend
npm install
```

**3. Run Development Servers:**

**Backend & Frontend:**

```bash
make dev
```
This will run the backend and frontend development servers.    Open your browser and navigate to the frontend development server URL (e.g., `http://localhost:5173/app`).

_Alternatively, you can run the backend and frontend development servers separately. For the backend, open a terminal in the `backend/` directory and run `langgraph dev`. The backend API will be available at `http://127.0.0.1:2024`. It will also open a browser window to the LangGraph UI. For the frontend, open a terminal in the `frontend/` directory and run `npm run dev`. The frontend will be available at `http://localhost:5173`._

## How the Power Plant Intelligence Agent Works

The core of the backend is a specialized LangGraph agent defined in `backend/src/agent/graph.py`. It follows a **two-phase approach** for comprehensive power plant data extraction:

![Agent Flow](./agent.png)

### **Phase 1: Organization-Level Data Collection**
1. **Organization Query Generation**: Creates specialized search queries for company-level information
2. **Web Research**: Searches for organizational data, financial information, and technology portfolio
3. **Data Extraction**: Extracts and structures data according to `org_level.json` template
4. **S3 Storage**: Stores organization JSON to AWS S3 with proper metadata

### **Phase 2: Plant-Level Data Collection**
1. **Plant Query Generation**: Creates targeted queries for specific power plant technical data
2. **Multi-Plant Detection**: Automatically identifies if organization has multiple plants
3. **Comprehensive Research**: Searches for:
   - Technical specifications (capacity, technology type, coordinates)
   - Operational data (CUF, generation, auxiliary power consumption)
   - Financial data (PPA details, contract information)
   - Grid connectivity (transmission lines, substations)
4. **Smart Data Processing**:
   - **Plant Type Logic**: Populates capacity fields based on technology (solar/wind/thermal)
   - **Fallback Calculations**: Calculates missing CUF using generation data
   - **Auxiliary Power Standards**: Applies industry-standard auxiliary consumption rates
5. **Template Compliance**: Filters data to match exact `plant_level.json` structure
6. **Sequential Plant Processing**: Creates separate JSONs for each plant with incremental IDs

### **Advanced Features**
- **Iterative Refinement**: Continues research until sufficient data is gathered
- **Source Verification**: Tracks and validates all information sources
- **Image Processing**: Extracts and uploads plant images to S3
- **Error Handling**: Comprehensive fallback mechanisms for missing data
- **Real-time Logging**: Detailed session tracking for debugging and monitoring

## Data Templates & Output Structure

The system generates structured JSON files that comply with predefined templates:

### **Organization Level** (`org_level.json`)
```json
{
  "pk": null,
  "sk": "scraped#org_details",
  "country_name": "India",
  "currency_in": "INR",
  "financial_year": "04-03",
  "currency_convert_to": null,
  "currency_listed": [],
  "technology_type": [
    {"enabled": "true", "name": "Renewable Energy"},
    {"enabled": "false", "name": "Biomass"}
  ]
}
```

### **Plant Level** (`plant_level.json`)
```json
{
  "sk": "plant#solar#1",
  "pk": null,
  "name": "Solar Power Plant",
  "plant_type": "solar",
  "plant_id": 1,
  "total_installed_capacity": "100",
  "installed_solar_capacity": "100",
  "installed_wind_capacity": "",
  "latitude": "23.4567",
  "longitude": "77.8901",
  "ppa_details": [
    {
      "capacity": "100",
      "start_date": "2020-01-01",
      "end_date": "2045-01-01",
      "respondents": [
        {
          "name": "State Electricity Board",
          "price": "3.50",
          "currency": "INR"
        }
      ]
    }
  ],
  "cuf": [
    {"value": "19.5%", "year": 2023},
    {"value": "18.7%", "year": 2022}
  ]
}
```

## Usage Examples

### **Single Plant Research**
```
Input: "Adani Solar Park Kamuthi"
Output: 
- 1 organization JSON
- 1 plant JSON with complete technical and financial data
```

### **Multi-Plant Organization**
```
Input: "ReNew Power"
Output:
- 1 organization JSON
- Multiple plant JSONs (Plant_1, Plant_2, Plant_3, etc.)
- Each plant with sequential IDs and plant-specific data
```

### **Key Data Points Extracted**
- **Technical**: Capacity, technology type, operational hours, efficiency
- **Financial**: PPA details, tariffs, contract duration, counterparties  
- **Operational**: CUF, generation data, auxiliary power consumption
- **Location**: GPS coordinates, address, grid connectivity
- **Regulatory**: Commissioning dates, closure dates, environmental compliance

## Deployment

In production, the backend server serves the optimized static frontend build. LangGraph requires a Redis instance and a Postgres database. Redis is used as a pub-sub broker to enable streaming real time output from background runs. Postgres is used to store assistants, threads, runs, persist thread state and long term memory, and to manage the state of the background task queue with 'exactly once' semantics. For more details on how to deploy the backend server, take a look at the [LangGraph Documentation](https://langchain-ai.github.io/langgraph/concepts/deployment_options/). Below is an example of how to build a Docker image that includes the optimized frontend build and the backend server and run it via `docker-compose`.

_Note: For the docker-compose.yml example you need a LangSmith API key, you can get one from [LangSmith](https://smith.langchain.com/settings)._

_Note: If you are not running the docker-compose.yml example or exposing the backend server to the public internet, you update the `apiUrl` in the `frontend/src/App.tsx` file your host. Currently the `apiUrl` is set to `http://localhost:8123` for docker-compose or `http://localhost:2024` for development._

**1. Build the Docker Image:**

   Run the following command from the **project root directory**:
   ```bash
   docker build -t gemini-fullstack-langgraph -f Dockerfile .
   ```
**2. Run the Production Server:**

   ```bash
   GEMINI_API_KEY=<your_gemini_api_key> LANGSMITH_API_KEY=<your_langsmith_api_key> docker-compose up
   ```

Open your browser and navigate to `http://localhost:8123/app/` to see the application. The API will be available at `http://localhost:8123`.

## Technologies Used

### **Frontend**
- [React](https://reactjs.org/) (with [Vite](https://vitejs.dev/)) - Modern frontend interface
- [Tailwind CSS](https://tailwindcss.com/) - Utility-first CSS framework
- [Shadcn UI](https://ui.shadcn.com/) - High-quality component library

### **Backend & AI**
- [LangGraph](https://github.com/langchain-ai/langgraph) - Advanced agent workflow orchestration
- [Google Gemini](https://ai.google.dev/models/gemini) - LLM for specialized power plant data extraction
- [FastAPI](https://fastapi.tiangolo.com/) - High-performance Python web framework

### **Data & Storage**
- [AWS S3](https://aws.amazon.com/s3/) - Structured JSON storage and image hosting
- [Google Custom Search API](https://developers.google.com/custom-search/v1/overview) - Web research capabilities
- [PostgreSQL](https://www.postgresql.org/) - State persistence and thread management
- [Redis](https://redis.io/) - Real-time streaming and pub-sub messaging

### **Power Plant Domain Expertise**
- **Industry Standards**: Built-in knowledge of power plant operational parameters
- **Technology-Specific Logic**: Solar, wind, thermal, biomass, and hybrid plant support
- **Regulatory Compliance**: Understanding of energy sector data requirements
- **Financial Analysis**: PPA structure extraction and contract analysis

## License

This project is licensed under the Apache License 2.0. See the [LICENSE](LICENSE) file for details. 