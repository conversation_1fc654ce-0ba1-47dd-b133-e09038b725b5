{"sk": "plant#plant_type#Plant_id", "pk": "default null", "annual_operational_hours": 8760, "auxiliary_power_consumed": [{"value": "The auxiliary (AUX) energy i.e energy consumed internally by the power plant its for its day-to-day operations. It is represented as a percentage of the gross energy generated by the plant/unnit.", "year": "Year for which auxiliary power data is reported(collect for last 5 years)"}], "closure_year": "when the plant is closed down.", "commencement_date": "The date of commercial operation of a specific unit. Represented in (yyyy-mm-ddThh:mm:ss:ms). Ex. 2012-1-01T00:00:00.000Z", "cuf": [{"value": "cuf value for the plant for year 2020", "year": 2020}, {"value": "cuf value for the plant for year 2021", "year": 2021}, {"value": "cuf value for the plant for year 2022", "year": 2022}, {"value": "cuf value for the plant for year 2023", "year": 2023}, {"value": "cuf value for the plant for year 2024", "year": 2024}], "gross_power_generation": [{"value": "Total energy generated by the unit/plant in a Financial Year", "year": "Year of the power generation data(collect for last 5 years)"}], "grid_connectivity_maps": [{"details": [{"capacity": "The rated capacity of the connection at this substation (e.g., in MW)", "latitude": "The geographic latitude of the substation", "longitude": "The geographic longitude of the substation", "projects": [{"distance": "The distance (e.g., in km) from the substation to that project"}], "substation_name": "The official name of the substation", "substation_type": "The classification and voltage level of the substation, including any regional or directional qualifier"}], "name": "", "s3_url": ""}], "installed_bess_capacity": "installed battery capacity of the plant", "installed_bess_capacity_unit": "MWh", "installed_biomass_capacity": "installed biomass capacity of the plant", "installed_biomass_capacity_unit": "MW", "installed_solar_capacity": "installed solar capacity of the plant", "installed_solar_capacity_unit": "MW", "installed_wind_capacity": "installed wind capacity of the plant", "installed_wind_capacity_unit": "MW", "latitude": "Latitude of the location of the plant", "longitude": "Longitude of the location of the plant", "mandatory_closure": "", "name": "name of the power plant", "plant_address": "District or city, State, Country", "plant_id": "A unique identifier assigned to this plant in your system (integer starts from 1)", "plant_images": [], "plant_lifetime": 25, "plant_type": "The technology or fuel type of the plant site", "potential_reference": {"lat": null, "long": null}, "ppa_details": [{"capacity": "The capacity covered by this PPA (typically in MW)", "capacity_unit": "The unit of that capacity (e.g., 'MW', 'kW')", "end_date": "The PPA's termination date (ISO format, YYYY-MM-DD). Typically 25 years from the start date.", "respondents": [{"capacity": "The capacity volume contracted by this respondent", "currency": "The currency in which the price is denominated (e.g., 'USD', 'INR')", "name": "The counterparty's name (utility, trader, corporate buyer, etc.)", "price": "The contracted price per unit of energy or capacity", "price_unit": "The basis for the price (e.g., '$/MWh', 'INR/kW-year')"}], "start_date": "The PPA's commencement date (ISO format, YYYY-MM-DD)", "tenure": "The numeric duration of the PPA (e.g., 20)", "tenure_type": "The unit for the tenure (e.g., 'Years', 'Months')"}], "remaining_useful_life": "The end-of-life of a specific unit. Represented in (yyyy-mm-ddThh:mm:ss:ms). Ex. 2012-1-01T00:00:00.000Z", "total_installed_capacity": "Total installed capacity of the plant in MW.", "total_installed_capacity_unit": "MW"}